"""
Pytest configuration and shared fixtures.
"""

import pytest
import asyncio
import sys
import os

# Add the parent directory to the path so we can import our modules
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Configure asyncio for pytest
@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()

# Set up pytest-asyncio
pytest_plugins = ('pytest_asyncio',)

# Configure logging for tests
import logging
logging.basicConfig(level=logging.DEBUG)

# Disable some noisy loggers during testing
logging.getLogger('alpaca').setLevel(logging.WARNING)
logging.getLogger('urllib3').setLevel(logging.WARNING)
