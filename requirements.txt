# Core trading and data libraries
alpaca-py==0.21.0
pandas==2.1.4
numpy==1.24.3

# Async and networking
aiohttp==3.9.1
websockets==12.0

# Configuration and environment
python-dotenv==1.0.0

# Logging and utilities
structlog==23.2.0
pytz==2023.3

# Data validation
pydantic==2.5.2

# Optional: For enhanced performance
uvloop==0.19.0; sys_platform != "win32"

# Testing dependencies
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-mock==3.12.0
pytest-cov==4.0.0
freezegun==1.2.2
