@echo off
REM Fix missing test dependencies
REM This script installs the missing pytest-cov and other test dependencies

echo ========================================
echo FIXING TEST DEPENDENCIES
echo ========================================
echo.

REM Check if virtual environment exists
if not exist "venv" (
    echo ERROR: Virtual environment not found!
    echo Please run 'setup_venv.bat' first to create the environment.
    echo.
    pause
    exit /b 1
)

echo Activating virtual environment...
call venv\Scripts\activate.bat
if errorlevel 1 (
    echo ERROR: Failed to activate virtual environment
    pause
    exit /b 1
)

echo.
echo Current Python environment:
python --version
echo.

echo Installing missing test dependencies...
echo.

echo Installing pytest-cov for coverage reporting...
pip install pytest-cov==4.0.0
if errorlevel 1 (
    echo WARNING: Failed to install pytest-cov
    echo You can still run tests without coverage using option 7 in run_tests.bat
)

echo.
echo Installing pytest-mock for mocking...
pip install pytest-mock==3.12.0
if errorlevel 1 (
    echo WARNING: Failed to install pytest-mock
)

echo.
echo Installing freezegun for time mocking...
pip install freezegun==1.2.2
if errorlevel 1 (
    echo WARNING: Failed to install freezegun
)

echo.
echo Upgrading all dependencies to latest versions...
pip install --upgrade -r requirements.txt

echo.
echo ========================================
echo DEPENDENCY FIX COMPLETE
echo ========================================
echo.

echo Testing the installation...
python -c "import pytest, pytest_cov, pytest_mock, freezegun; print('✅ All test dependencies installed successfully!')"
if errorlevel 1 (
    echo ⚠️  Some dependencies may still be missing, but basic testing should work.
    echo You can use the simple test runner (option 7) if needed.
)

echo.
echo You can now run tests with coverage:
echo - run_tests.bat (choose option 6 for coverage)
echo - python run_tests.py --coverage
echo.

echo Press any key to exit...
pause >nul
