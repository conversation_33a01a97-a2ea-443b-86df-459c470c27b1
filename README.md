# Autonomous Trading Bot

A high-performance, production-ready algorithmic trading system built with Python and the Alpaca API. This bot implements a Bollinger Bands mean-reversion strategy with comprehensive risk management and real-time market data processing.

## 🚀 Key Features

- **Event-Driven Architecture**: Real-time WebSocket data streaming with asyncio
- **Advanced Risk Management**: Volatility-adjusted position sizing, drawdown limits, and portfolio safeguards
- **Dynamic Universe Selection**: Automatically selects liquid stocks based on volume and price criteria
- **Bollinger Bands Strategy**: Mean-reversion trading with automated entry/exit signals
- **Bracket Orders**: Every trade includes automatic take-profit and stop-loss orders
- **Rate Limiting**: Respects Alpaca's 200 requests/minute API limit
- **Comprehensive Logging**: Structured logging with both file and console output

## 📋 Requirements

- Python 3.8+
- Alpaca Paper Trading Account (recommended) or Live Account
- Minimum $1,000 account balance for effective operation

## 🛠️ Installation

1. **Clone or download the project files**

2. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Set up environment variables**:
   ```bash
   cp .env.template .env
   ```
   
   Edit `.env` and add your Alpaca API credentials:
   ```
   ALPACA_API_KEY=your_alpaca_api_key_here
   ALPACA_SECRET_KEY=your_alpaca_secret_key_here
   ALPACA_BASE_URL=https://paper-api.alpaca.markets
   ```

4. **Get Alpaca API Keys**:
   - Sign up at [Alpaca](https://alpaca.markets/)
   - Go to your dashboard and generate API keys
   - **Important**: Start with paper trading to test the system

## ⚙️ Configuration

Key parameters can be adjusted in `config.py`:

### Trading Parameters
- `UNIVERSE_SIZE`: Number of stocks to trade (default: 25)
- `BB_PERIOD`: Bollinger Bands period (default: 20)
- `BB_STD_DEV`: Standard deviations for bands (default: 2.0)
- `ATR_PERIOD`: ATR period for volatility (default: 14)

### Risk Management
- `MAX_POSITION_RISK`: Maximum risk per trade (default: 1.5%)
- `MAX_DAILY_DRAWDOWN`: Daily drawdown limit (default: 4%)
- `MAX_TOTAL_DRAWDOWN`: Total drawdown limit (default: 20%)
- `MAX_CONCURRENT_POSITIONS`: Maximum open positions (default: 15)

### Universe Selection
- `MIN_PRICE`: Minimum stock price (default: $10)
- `MAX_PRICE`: Maximum stock price (default: $2000)

## 🚀 Running the Bot

1. **Start the trading bot**:
   ```bash
   python main.py
   ```

2. **Monitor the logs**:
   - Console output shows real-time status
   - Detailed logs are saved to `trading_bot.log`
   - Bot will automatically start trading when markets are open

3. **Stop the bot**:
   - Press `Ctrl+C` for graceful shutdown
   - All pending orders will be cancelled automatically

## 📊 Strategy Overview

### Bollinger Bands Mean Reversion
- **Entry Signals**:
  - Long: Price crosses below lower Bollinger Band
  - Short: Price crosses above upper Bollinger Band
- **Exit Signals**:
  - Take Profit: Price reaches middle Bollinger Band (moving average)
  - Stop Loss: 2x ATR distance from entry price

### Risk Management
- **Position Sizing**: Based on 1.5% account risk per trade
- **Volatility Adjustment**: Uses 14-period ATR for stop-loss calculation
- **Portfolio Limits**: Maximum 15 concurrent positions
- **Drawdown Protection**: Halts trading if limits are exceeded

## 📁 Project Structure

```
├── main.py                 # Main application orchestrator
├── config.py              # Configuration parameters
├── utils.py               # Utility functions and technical analysis
├── universe_selector.py   # Dynamic stock universe selection
├── strategy.py           # Bollinger Bands trading strategy
├── risk_manager.py       # Risk management and position sizing
├── order_manager.py      # Order execution and position tracking
├── requirements.txt      # Python dependencies
├── .env.template        # Environment variables template
└── README.md           # This file
```

## 🔧 Testing and Validation

### Before Live Trading
1. **Paper Trading**: Always test with paper trading first
2. **Monitor Performance**: Run for at least 1-2 weeks to validate strategy
3. **Check Risk Metrics**: Ensure drawdown limits are working correctly
4. **Verify Orders**: Confirm bracket orders are being placed correctly

### Recommended Testing Approach
```bash
# Install testing dependencies
pip install pytest pytest-asyncio

# Create test files (examples)
# test_strategy.py - Test signal generation
# test_risk_manager.py - Test position sizing
# test_utils.py - Test technical indicators

# Run tests
pytest tests/
```

## 📈 Monitoring and Maintenance

### Key Metrics to Monitor
- Daily and total drawdown percentages
- Number of open positions
- Win rate and average trade duration
- API rate limit usage
- Failed order count

### Log Analysis
```bash
# View recent trades
tail -f trading_bot.log | grep "order placed"

# Check risk metrics
tail -f trading_bot.log | grep "BOT STATUS"

# Monitor errors
tail -f trading_bot.log | grep "ERROR"
```

## ⚠️ Important Warnings

1. **Start with Paper Trading**: Never begin with live money
2. **Monitor Closely**: Especially during the first few days
3. **Risk Management**: The bot will halt trading if risk limits are exceeded
4. **Market Hours**: Bot only trades during regular market hours (9:30 AM - 4:00 PM ET)
5. **Internet Connection**: Ensure stable connection for WebSocket data
6. **API Limits**: Bot respects rate limits but monitor usage

## 🛡️ Safety Features

- **Kill Switch**: Automatic trading halt on excessive drawdown
- **Position Limits**: Maximum concurrent positions enforced
- **Market Hours Check**: Only trades during market hours
- **Graceful Shutdown**: Cancels pending orders on exit
- **Error Handling**: Comprehensive exception handling throughout

## 📞 Support and Troubleshooting

### Common Issues
1. **"No trading universe selected"**: Check API credentials and internet connection
2. **"Rate limit reached"**: Bot will automatically wait and retry
3. **"Trading halted"**: Check risk metrics, may need manual reset
4. **WebSocket disconnection**: Bot will automatically reconnect

### Getting Help
- Check logs in `trading_bot.log` for detailed error messages
- Verify API credentials in `.env` file
- Ensure sufficient account balance for position sizing
- Confirm market is open during trading hours

## 📄 License

This project is for educational and research purposes. Use at your own risk. Always test thoroughly before deploying with real money.

---

**Disclaimer**: Trading involves substantial risk of loss. Past performance does not guarantee future results. This software is provided as-is without warranty.
