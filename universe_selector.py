"""
Universe selection engine for dynamically selecting tradable stocks
based on liquidity, price range, and other criteria.
"""

import asyncio
import logging
from typing import List, Set, Dict, Any
from datetime import datetime, timedelta
import pandas as pd
from alpaca.trading.client import TradingClient
from alpaca.trading.requests import GetAssetsRequest
from alpaca.trading.enums import AssetClass, AssetStatus
from alpaca.data.historical import StockHistoricalDataClient
from alpaca.data.requests import StockBarsRequest
from alpaca.data.timeframe import TimeFrame
from config import TradingConfig
from utils import validate_symbol, RateLimiter

logger = logging.getLogger(__name__)

class UniverseSelector:
    """
    Dynamically selects a universe of tradable stocks based on liquidity and other criteria.
    """
    
    def __init__(self, trading_client: TradingClient, data_client: StockHistoricalDataClient):
        self.trading_client = trading_client
        self.data_client = data_client
        self.current_universe: Set[str] = set()
        self.last_update: datetime = datetime.min
        self.rate_limiter = RateLimiter(
            TradingConfig.API_RATE_LIMIT, 
            TradingConfig.API_RATE_WINDOW
        )
        
    async def get_tradable_assets(self) -> List[Dict[str, Any]]:
        """
        Get all tradable US equity assets from Alpaca.
        
        Returns:
            List of asset dictionaries with symbol and other metadata
        """
        try:
            if not self.rate_limiter.can_make_call():
                logger.warning("Rate limit reached, skipping asset fetch")
                return []
            
            # Get all US equity assets that are active and tradable
            request = GetAssetsRequest(
                status=AssetStatus.ACTIVE,
                asset_class=AssetClass.US_EQUITY
            )
            
            assets = self.trading_client.get_all_assets(request)
            self.rate_limiter.record_call()
            
            # Filter for tradable and shortable assets
            tradable_assets = []
            for asset in assets:
                if (asset.tradable and 
                    asset.shortable and 
                    validate_symbol(asset.symbol) and
                    TradingConfig.MIN_PRICE <= float(asset.price or 0) <= TradingConfig.MAX_PRICE):
                    
                    tradable_assets.append({
                        'symbol': asset.symbol,
                        'name': asset.name,
                        'exchange': asset.exchange,
                        'price': float(asset.price or 0)
                    })
            
            logger.info(f"Found {len(tradable_assets)} tradable assets")
            return tradable_assets
            
        except Exception as e:
            logger.error(f"Error fetching tradable assets: {e}")
            return []
    
    async def calculate_liquidity_metrics(self, symbols: List[str]) -> Dict[str, float]:
        """
        Calculate 30-day average dollar volume for given symbols.
        
        Args:
            symbols: List of stock symbols
            
        Returns:
            Dictionary mapping symbols to their average dollar volume
        """
        liquidity_metrics = {}
        
        try:
            # Calculate date range for 30 days of data
            end_date = datetime.now()
            start_date = end_date - timedelta(days=45)  # Extra days to account for weekends
            
            # Process symbols in batches to respect rate limits
            batch_size = 50  # Alpaca allows up to 100 symbols per request
            
            for i in range(0, len(symbols), batch_size):
                batch_symbols = symbols[i:i + batch_size]
                
                if not self.rate_limiter.can_make_call():
                    logger.warning("Rate limit reached, pausing liquidity calculation")
                    await asyncio.sleep(5)
                    continue
                
                try:
                    # Get daily bars for the batch
                    request = StockBarsRequest(
                        symbol_or_symbols=batch_symbols,
                        timeframe=TimeFrame.Day,
                        start=start_date,
                        end=end_date
                    )
                    
                    bars = self.data_client.get_stock_bars(request)
                    self.rate_limiter.record_call()
                    
                    # Calculate average dollar volume for each symbol
                    for symbol in batch_symbols:
                        if symbol in bars.data:
                            symbol_bars = bars.data[symbol]
                            if len(symbol_bars) > 0:
                                # Calculate dollar volume for each day
                                dollar_volumes = []
                                for bar in symbol_bars:
                                    dollar_volume = bar.close * bar.volume
                                    dollar_volumes.append(dollar_volume)
                                
                                # Calculate 30-day average
                                if dollar_volumes:
                                    avg_dollar_volume = sum(dollar_volumes) / len(dollar_volumes)
                                    liquidity_metrics[symbol] = avg_dollar_volume
                
                except Exception as e:
                    logger.error(f"Error calculating liquidity for batch {i}: {e}")
                    continue
                
                # Small delay between batches to be respectful of API
                await asyncio.sleep(0.1)
        
        except Exception as e:
            logger.error(f"Error in liquidity calculation: {e}")
        
        return liquidity_metrics
    
    async def select_universe(self) -> Set[str]:
        """
        Select the trading universe based on liquidity and other criteria.
        
        Returns:
            Set of selected stock symbols
        """
        try:
            logger.info("Starting universe selection process")
            
            # Get all tradable assets
            assets = await self.get_tradable_assets()
            if not assets:
                logger.warning("No tradable assets found, keeping current universe")
                return self.current_universe
            
            # Extract symbols for liquidity analysis
            symbols = [asset['symbol'] for asset in assets]
            
            # Calculate liquidity metrics
            logger.info(f"Calculating liquidity metrics for {len(symbols)} symbols")
            liquidity_metrics = await self.calculate_liquidity_metrics(symbols)
            
            if not liquidity_metrics:
                logger.warning("No liquidity data available, keeping current universe")
                return self.current_universe
            
            # Sort by liquidity (average dollar volume) and select top N
            sorted_symbols = sorted(
                liquidity_metrics.items(),
                key=lambda x: x[1],
                reverse=True
            )
            
            # Select top symbols up to universe size
            selected_symbols = set()
            for symbol, avg_volume in sorted_symbols[:TradingConfig.UNIVERSE_SIZE * 2]:  # Get extra in case some are filtered
                if len(selected_symbols) >= TradingConfig.UNIVERSE_SIZE:
                    break
                
                # Additional validation
                if (validate_symbol(symbol) and 
                    avg_volume > 1_000_000):  # Minimum $1M average daily volume
                    selected_symbols.add(symbol)
            
            logger.info(f"Selected universe of {len(selected_symbols)} symbols: {sorted(selected_symbols)}")
            
            # Log top symbols by liquidity
            top_10 = sorted_symbols[:10]
            logger.info("Top 10 most liquid symbols:")
            for symbol, volume in top_10:
                logger.info(f"  {symbol}: ${volume:,.0f} avg daily volume")
            
            return selected_symbols
            
        except Exception as e:
            logger.error(f"Error in universe selection: {e}")
            return self.current_universe
    
    async def update_universe(self) -> bool:
        """
        Update the trading universe if enough time has passed.
        
        Returns:
            bool: True if universe was updated, False otherwise
        """
        try:
            now = datetime.now()
            time_since_update = (now - self.last_update).total_seconds()
            
            if time_since_update < TradingConfig.UNIVERSE_REFRESH_INTERVAL:
                return False
            
            logger.info("Updating trading universe")
            new_universe = await self.select_universe()
            
            # Check if universe has changed significantly
            if new_universe != self.current_universe:
                added_symbols = new_universe - self.current_universe
                removed_symbols = self.current_universe - new_universe
                
                if added_symbols:
                    logger.info(f"Added symbols to universe: {sorted(added_symbols)}")
                if removed_symbols:
                    logger.info(f"Removed symbols from universe: {sorted(removed_symbols)}")
                
                self.current_universe = new_universe
                self.last_update = now
                return True
            else:
                logger.info("Universe unchanged")
                self.last_update = now
                return False
                
        except Exception as e:
            logger.error(f"Error updating universe: {e}")
            return False
    
    def get_current_universe(self) -> Set[str]:
        """Get the current trading universe."""
        return self.current_universe.copy()
    
    def is_in_universe(self, symbol: str) -> bool:
        """Check if a symbol is in the current trading universe."""
        return symbol in self.current_universe
