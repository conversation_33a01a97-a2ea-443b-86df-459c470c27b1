["tests/test_integration.py::TestEndToEndScenarios::test_multiple_symbol_trading", "tests/test_integration.py::TestEndToEndScenarios::test_risk_management_across_portfolio", "tests/test_integration.py::TestOrderManagerIntegration::test_bracket_order_submission", "tests/test_integration.py::TestOrderManagerIntegration::test_order_failure_handling", "tests/test_integration.py::TestOrderManagerIntegration::test_position_cache_update", "tests/test_integration.py::TestTradingWorkflow::test_complete_long_trade_workflow", "tests/test_integration.py::TestTradingWorkflow::test_complete_short_trade_workflow", "tests/test_integration.py::TestTradingWorkflow::test_drawdown_halts_trading", "tests/test_integration.py::TestTradingWorkflow::test_position_limit_enforcement", "tests/test_integration.py::TestTradingWorkflow::test_risk_management_blocks_oversized_trade", "tests/test_integration.py::TestUniverseSelectionIntegration::test_universe_selection_workflow", "tests/test_risk_manager.py::TestRiskManager::test_calculate_position_size", "tests/test_risk_manager.py::TestRiskManager::test_calculate_position_size_when_halted", "tests/test_risk_manager.py::TestRiskManager::test_daily_drawdown_limit", "tests/test_risk_manager.py::TestRiskManager::test_daily_reset", "tests/test_risk_manager.py::TestRiskManager::test_force_resume_trading", "tests/test_risk_manager.py::TestRiskManager::test_force_resume_when_not_halted", "tests/test_risk_manager.py::TestRiskManager::test_get_risk_metrics", "tests/test_risk_manager.py::TestRiskManager::test_halt_trading_functionality", "tests/test_risk_manager.py::TestRiskManager::test_initialization", "tests/test_risk_manager.py::TestRiskManager::test_initialize_equity", "tests/test_risk_manager.py::TestRiskManager::test_position_count_limit", "tests/test_risk_manager.py::TestRiskManager::test_position_size_without_initialization", "tests/test_risk_manager.py::TestRiskManager::test_risk_limits_without_initialization", "tests/test_risk_manager.py::TestRiskManager::test_total_drawdown_limit", "tests/test_risk_manager.py::TestRiskManager::test_update_equity_drawdown", "tests/test_risk_manager.py::TestRiskManager::test_update_equity_new_high", "tests/test_risk_manager.py::TestRiskManager::test_validate_trade_approved", "tests/test_risk_manager.py::TestRiskManager::test_validate_trade_invalid_prices", "tests/test_risk_manager.py::TestRiskManager::test_validate_trade_rejected_halted", "tests/test_strategy.py::TestTradingStrategy::test_add_bar_data", "tests/test_strategy.py::TestTradingStrategy::test_calculate_trade_levels", "tests/test_strategy.py::TestTradingStrategy::test_clear_symbol_data", "tests/test_strategy.py::TestTradingStrategy::test_dataframe_conversion", "tests/test_strategy.py::TestTradingStrategy::test_insufficient_data_for_indicators", "tests/test_strategy.py::TestTradingStrategy::test_long_entry_signal_generation", "tests/test_strategy.py::TestTradingStrategy::test_long_exit_signal_generation", "tests/test_strategy.py::TestTradingStrategy::test_no_signal_with_existing_position", "tests/test_strategy.py::TestTradingStrategy::test_position_tracking", "tests/test_strategy.py::TestTradingStrategy::test_short_entry_signal_generation", "tests/test_strategy.py::TestTradingStrategy::test_short_exit_signal_generation", "tests/test_strategy.py::TestTradingStrategy::test_strategy_stats", "tests/test_strategy.py::TestTradingStrategy::test_sufficient_data_for_indicators", "tests/test_system.py::TestSystemResilience::test_api_error_recovery", "tests/test_system.py::TestSystemResilience::test_memory_management", "tests/test_system.py::TestSystemResilience::test_websocket_reconnection_simulation", "tests/test_system.py::TestTradingBotSystem::test_bot_initialization", "tests/test_system.py::TestTradingBotSystem::test_error_handling_in_bar_processing", "tests/test_system.py::TestTradingBotSystem::test_graceful_shutdown", "tests/test_system.py::TestTradingBotSystem::test_market_closed_behavior", "tests/test_system.py::TestTradingBotSystem::test_on_bar_processing", "tests/test_system.py::TestTradingBotSystem::test_periodic_tasks_execution", "tests/test_system.py::TestTradingBotSystem::test_risk_management_integration", "tests/test_system.py::TestTradingBotSystem::test_signal_processing_workflow", "tests/test_system.py::TestTradingBotSystem::test_universe_update_and_subscription_changes", "tests/test_utils.py::TestMarketHours::test_market_closed_before_hours", "tests/test_utils.py::TestMarketHours::test_market_closed_weekend", "tests/test_utils.py::TestMarketHours::test_market_open_during_hours", "tests/test_utils.py::TestPositionSizing::test_position_size_calculation", "tests/test_utils.py::TestPositionSizing::test_position_size_minimum_one_share", "tests/test_utils.py::TestPositionSizing::test_position_size_zero_risk", "tests/test_utils.py::TestRateLimiter::test_rate_limiter_allows_calls", "tests/test_utils.py::TestRateLimiter::test_rate_limiter_resets_after_window", "tests/test_utils.py::TestTechnicalIndicators::test_atr_calculation", "tests/test_utils.py::TestTechnicalIndicators::test_bollinger_bands_calculation", "tests/test_utils.py::TestTechnicalIndicators::test_bollinger_bands_insufficient_data", "tests/test_utils.py::TestUtilityFunctions::test_calculate_drawdown", "tests/test_utils.py::TestUtilityFunctions::test_is_new_trading_day", "tests/test_utils.py::TestUtilityFunctions::test_validate_symbol_invalid", "tests/test_utils.py::TestUtilityFunctions::test_validate_symbol_valid"]