"""
Test fixtures and mock data for testing the trading bot.
"""

import pytest
from datetime import datetime, timedelta
from unittest.mock import MagicM<PERSON>, <PERSON><PERSON>
from typing import List, Dict, Any
import pandas as pd
import numpy as np

# Mock Alpaca objects
class MockBar:
    """Mock Alpaca Bar object."""
    
    def __init__(self, symbol: str, timestamp: datetime, open_price: float, 
                 high: float, low: float, close: float, volume: int):
        self.symbol = symbol
        self.timestamp = timestamp
        self.open = open_price
        self.high = high
        self.low = low
        self.close = close
        self.volume = volume

class MockPosition:
    """Mock Alpaca Position object."""
    
    def __init__(self, symbol: str, qty: float, market_value: float = None):
        self.symbol = symbol
        self.qty = str(qty)
        self.market_value = str(market_value or qty * 100)  # Default $100 per share

class MockAccount:
    """Mock Alpaca Account object."""
    
    def __init__(self, equity: float, buying_power: float = None, status: str = "ACTIVE"):
        self.equity = str(equity)
        self.buying_power = str(buying_power or equity * 2)
        self.status = status

class MockOrder:
    """Mock Alpaca Order object."""
    
    def __init__(self, order_id: str, symbol: str, qty: int, side: str, status: str = "new"):
        self.id = order_id
        self.symbol = symbol
        self.qty = str(qty)
        self.side = side
        self.status = status

class MockAsset:
    """Mock Alpaca Asset object."""
    
    def __init__(self, symbol: str, name: str, tradable: bool = True, 
                 shortable: bool = True, price: float = 100.0):
        self.symbol = symbol
        self.name = name
        self.tradable = tradable
        self.shortable = shortable
        self.price = str(price)
        self.exchange = "NASDAQ"

@pytest.fixture
def mock_trading_client():
    """Create a mock trading client."""
    client = MagicMock()
    
    # Mock account
    client.get_account.return_value = MockAccount(10000.0)
    
    # Mock positions
    client.get_all_positions.return_value = []
    
    # Mock orders
    client.get_orders.return_value = []
    client.submit_order.return_value = MockOrder("order_123", "AAPL", 10, "buy")
    client.cancel_order_by_id.return_value = True
    
    # Mock assets
    client.get_all_assets.return_value = [
        MockAsset("AAPL", "Apple Inc.", price=150.0),
        MockAsset("MSFT", "Microsoft Corp.", price=300.0),
        MockAsset("GOOGL", "Alphabet Inc.", price=2500.0),
    ]
    
    return client

@pytest.fixture
def mock_data_client():
    """Create a mock data client."""
    client = MagicMock()
    
    # Mock bars data
    mock_bars = MagicMock()
    mock_bars.data = {
        "AAPL": [
            MockBar("AAPL", datetime.now() - timedelta(days=i), 
                   150.0 + i, 151.0 + i, 149.0 + i, 150.5 + i, 1000000)
            for i in range(30)
        ]
    }
    client.get_stock_bars.return_value = mock_bars
    
    return client

@pytest.fixture
def sample_price_data():
    """Generate sample price data for testing."""
    np.random.seed(42)  # For reproducible tests
    
    # Generate 50 days of realistic price data
    base_price = 100.0
    returns = np.random.normal(0, 0.02, 50)  # 2% daily volatility
    prices = [base_price]
    
    for ret in returns:
        new_price = prices[-1] * (1 + ret)
        prices.append(max(new_price, 1.0))  # Ensure positive prices
    
    return prices[1:]  # Remove the base price

@pytest.fixture
def bollinger_bands_test_data():
    """Generate test data that will create clear Bollinger Bands signals."""
    # Create data with clear mean reversion patterns
    base_prices = [100] * 20  # Stable period for BB calculation
    
    # Add a clear downward spike (should trigger long signal)
    spike_down = [98, 95, 92, 90, 88]  # Below lower BB
    recovery = [90, 92, 95, 98, 100]  # Recovery to middle BB
    
    # Add a clear upward spike (should trigger short signal)
    spike_up = [102, 105, 108, 110, 112]  # Above upper BB
    decline = [110, 108, 105, 102, 100]  # Decline to middle BB
    
    return base_prices + spike_down + recovery + spike_up + decline

@pytest.fixture
def high_volatility_data():
    """Generate high volatility data for ATR testing."""
    np.random.seed(123)
    
    data = []
    base_price = 100.0
    
    for i in range(30):
        # High volatility with large daily ranges
        daily_range = np.random.uniform(3, 8)  # 3-8% daily range
        open_price = base_price
        high = open_price + daily_range / 2
        low = open_price - daily_range / 2
        close = np.random.uniform(low, high)
        
        data.append({
            'open': open_price,
            'high': high,
            'low': low,
            'close': close,
            'volume': np.random.randint(500000, 2000000)
        })
        
        base_price = close
    
    return data

@pytest.fixture
def mock_websocket_stream():
    """Create a mock WebSocket stream."""
    stream = MagicMock()
    stream.subscribe_bars = MagicMock()
    stream.unsubscribe_bars = MagicMock()
    stream._run_forever = MagicMock()
    stream.close = MagicMock()
    return stream

def create_test_bars(symbol: str, prices: List[float], 
                    start_time: datetime = None) -> List[MockBar]:
    """Create a list of test bars from price data."""
    if start_time is None:
        start_time = datetime.now() - timedelta(minutes=len(prices))
    
    bars = []
    for i, price in enumerate(prices):
        timestamp = start_time + timedelta(minutes=i)
        bar = MockBar(
            symbol=symbol,
            timestamp=timestamp,
            open_price=price,
            high=price + 0.5,
            low=price - 0.5,
            close=price,
            volume=1000000
        )
        bars.append(bar)
    
    return bars

def create_realistic_ohlcv_data(days: int = 30, base_price: float = 100.0) -> pd.DataFrame:
    """Create realistic OHLCV data for testing."""
    np.random.seed(42)
    
    dates = pd.date_range(start=datetime.now() - timedelta(days=days), 
                         periods=days, freq='D')
    
    data = []
    current_price = base_price
    
    for date in dates:
        # Generate realistic daily OHLCV
        daily_return = np.random.normal(0, 0.02)  # 2% daily volatility
        open_price = current_price
        
        # High and low based on intraday volatility
        intraday_range = abs(np.random.normal(0, 0.015))  # 1.5% intraday range
        high = open_price * (1 + intraday_range)
        low = open_price * (1 - intraday_range)
        
        # Close price
        close = open_price * (1 + daily_return)
        close = max(min(close, high), low)  # Ensure close is within range
        
        # Volume with some randomness
        volume = int(np.random.normal(1000000, 200000))
        volume = max(volume, 100000)  # Minimum volume
        
        data.append({
            'timestamp': date,
            'open': round(open_price, 2),
            'high': round(high, 2),
            'low': round(low, 2),
            'close': round(close, 2),
            'volume': volume
        })
        
        current_price = close
    
    return pd.DataFrame(data).set_index('timestamp')

class MockRateLimiter:
    """Mock rate limiter that always allows calls."""
    
    def __init__(self):
        self.calls = []
    
    def can_make_call(self):
        return True
    
    def record_call(self):
        self.calls.append(datetime.now())

# Test scenarios for different market conditions
MARKET_SCENARIOS = {
    'trending_up': {
        'description': 'Strong upward trend',
        'prices': [100 + i * 0.5 for i in range(50)],  # Steady upward trend
    },
    'trending_down': {
        'description': 'Strong downward trend', 
        'prices': [100 - i * 0.3 for i in range(50)],  # Steady downward trend
    },
    'sideways': {
        'description': 'Sideways/ranging market',
        'prices': [100 + 2 * np.sin(i * 0.3) for i in range(50)],  # Oscillating around 100
    },
    'volatile': {
        'description': 'High volatility market',
        'prices': [100 + np.random.normal(0, 3) for _ in range(50)],  # High volatility
    },
    'gap_up': {
        'description': 'Gap up scenario',
        'prices': [100] * 20 + [110] * 20 + [115] * 10,  # Price gaps
    },
    'gap_down': {
        'description': 'Gap down scenario', 
        'prices': [100] * 20 + [90] * 20 + [85] * 10,  # Price gaps down
    }
}

@pytest.fixture(params=MARKET_SCENARIOS.keys())
def market_scenario(request):
    """Parametrized fixture for different market scenarios."""
    scenario_name = request.param
    scenario_data = MARKET_SCENARIOS[scenario_name]
    
    return {
        'name': scenario_name,
        'description': scenario_data['description'],
        'prices': scenario_data['prices']
    }
