{"note": "This file is an internal implementation detail to speed up HTML report generation. Its format can change at any time. You might be looking for the JSON report: https://coverage.rtfd.io/cmd.html#cmd-json", "format": 5, "version": "7.10.5", "globals": "fc5e1a9066435a9acd597c932de7fe9d", "files": {"config_py": {"hash": "31c0d12cf7aa4e8c44c137691ba87387", "index": {"url": "config_py.html", "file": "config.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 47, "n_excluded": 0, "n_missing": 2, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "main_py": {"hash": "d3cbe4d097f857b6ddc55eb05bc9c363", "index": {"url": "main_py.html", "file": "main.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 210, "n_excluded": 0, "n_missing": 78, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "order_manager_py": {"hash": "a963d43396177c2f6361d2d4545d47a1", "index": {"url": "order_manager_py.html", "file": "order_manager.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 158, "n_excluded": 0, "n_missing": 106, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "risk_manager_py": {"hash": "19ef418571dbbd31dce39820f684cef3", "index": {"url": "risk_manager_py.html", "file": "risk_manager.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 115, "n_excluded": 0, "n_missing": 40, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "run_tests_py": {"hash": "d4e24be69395f4b9fa11fef45d4304ab", "index": {"url": "run_tests_py.html", "file": "run_tests.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 77, "n_excluded": 0, "n_missing": 77, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "run_tests_simple_py": {"hash": "fcfceac4b4c14b1db96c5c550551f46a", "index": {"url": "run_tests_simple_py.html", "file": "run_tests_simple.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 59, "n_excluded": 0, "n_missing": 59, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "strategy_py": {"hash": "7e4696a7b5f860ad4b8e017214fc75e3", "index": {"url": "strategy_py.html", "file": "strategy.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 128, "n_excluded": 0, "n_missing": 39, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "test_quick_py": {"hash": "71918a6ad271276c366ae0aa31308136", "index": {"url": "test_quick_py.html", "file": "test_quick.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 139, "n_excluded": 0, "n_missing": 139, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531___init___py": {"hash": "d3dfcd3b5b46a701322c588a7226f069", "index": {"url": "z_a44f0ac069e85531___init___py.html", "file": "tests\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_conftest_py": {"hash": "dee569a9368cba39fce48c8780b9d3b2", "index": {"url": "z_a44f0ac069e85531_conftest_py.html", "file": "tests\\conftest.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 15, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_fixtures_py": {"hash": "49ec3e4029f94463fbac568b504e02a1", "index": {"url": "z_a44f0ac069e85531_fixtures_py.html", "file": "tests\\fixtures.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 137, "n_excluded": 0, "n_missing": 84, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_integration_py": {"hash": "7de60dfe28c6802b33331a68d797521d", "index": {"url": "z_a44f0ac069e85531_test_integration_py.html", "file": "tests\\test_integration.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 167, "n_excluded": 0, "n_missing": 167, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_risk_manager_py": {"hash": "54a96b6a7db6a7e6dcbdbec90b9632dc", "index": {"url": "z_a44f0ac069e85531_test_risk_manager_py.html", "file": "tests\\test_risk_manager.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 139, "n_excluded": 0, "n_missing": 139, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_strategy_py": {"hash": "526b1ad23c408275ace05d2c9e9aa894", "index": {"url": "z_a44f0ac069e85531_test_strategy_py.html", "file": "tests\\test_strategy.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 152, "n_excluded": 0, "n_missing": 152, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_system_py": {"hash": "45744885478ed042e9cfd3710fe532f4", "index": {"url": "z_a44f0ac069e85531_test_system_py.html", "file": "tests\\test_system.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 189, "n_excluded": 0, "n_missing": 5, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_utils_py": {"hash": "27bf14d8a9de076a0ed9f3570d4677ce", "index": {"url": "z_a44f0ac069e85531_test_utils_py.html", "file": "tests\\test_utils.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 124, "n_excluded": 0, "n_missing": 124, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "universe_selector_py": {"hash": "53baf1a7053b84f217f98b94cfef4ead", "index": {"url": "universe_selector_py.html", "file": "universe_selector.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 129, "n_excluded": 0, "n_missing": 25, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "utils_py": {"hash": "4f8808b1843aee2cab79ae0657cf4e3f", "index": {"url": "utils_py.html", "file": "utils.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 86, "n_excluded": 0, "n_missing": 22, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}}}