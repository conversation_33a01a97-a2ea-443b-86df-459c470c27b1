"""
Configuration module for the autonomous trading bot.
All key parameters and settings are centralized here.
"""

import os
from typing import List
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class TradingConfig:
    """Central configuration class for all trading parameters."""
    
    # API Configuration
    ALPACA_API_KEY = os.getenv("ALPACA_API_KEY")
    ALPACA_SECRET_KEY = os.getenv("ALPACA_SECRET_KEY")
    ALPACA_BASE_URL = os.getenv("ALPACA_BASE_URL", "https://paper-api.alpaca.markets")
    
    # Trading Universe Configuration
    UNIVERSE_SIZE = 25  # Number of stocks to trade
    MIN_PRICE = 10.0    # Minimum stock price
    MAX_PRICE = 2000.0  # Maximum stock price
    UNIVERSE_REFRESH_INTERVAL = 60  # Seconds between universe updates
    
    # Strategy Parameters - Bollinger Bands
    BB_PERIOD = 20      # Bollinger Bands period
    BB_STD_DEV = 2.0    # Standard deviations for bands
    ATR_PERIOD = 14     # ATR period for volatility calculation
    
    # Risk Management Parameters
    MAX_POSITION_RISK = 0.015  # 1.5% max risk per trade
    ATR_STOP_MULTIPLIER = 2.0  # Stop loss distance (ATR * multiplier)
    MAX_DAILY_DRAWDOWN = 0.04  # 4% max daily drawdown
    MAX_TOTAL_DRAWDOWN = 0.20  # 20% max total drawdown
    
    # Position Management
    POSITION_CACHE_REFRESH_INTERVAL = 12  # Seconds between position cache updates
    MAX_CONCURRENT_POSITIONS = 15  # Maximum number of open positions
    
    # API Rate Limiting
    API_RATE_LIMIT = 200  # Requests per minute
    API_RATE_WINDOW = 60  # Rate limit window in seconds
    
    # Market Hours (ET)
    MARKET_OPEN_HOUR = 9
    MARKET_OPEN_MINUTE = 30
    MARKET_CLOSE_HOUR = 16
    MARKET_CLOSE_MINUTE = 0
    
    # Data Management
    MAX_BARS_MEMORY = 100  # Maximum bars to keep in memory per symbol
    
    # Logging Configuration
    LOG_LEVEL = "INFO"
    LOG_FILE = "trading_bot.log"
    LOG_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    
    # WebSocket Configuration
    WS_RECONNECT_DELAY = 5  # Seconds to wait before reconnecting
    WS_MAX_RECONNECT_ATTEMPTS = 10
    
    @classmethod
    def validate_config(cls) -> bool:
        """Validate that all required configuration is present."""
        required_env_vars = [
            "ALPACA_API_KEY",
            "ALPACA_SECRET_KEY"
        ]
        
        missing_vars = []
        for var in required_env_vars:
            if not getattr(cls, var):
                missing_vars.append(var)
        
        if missing_vars:
            raise ValueError(f"Missing required environment variables: {missing_vars}")
        
        return True
    
    @classmethod
    def get_excluded_symbols(cls) -> List[str]:
        """Get list of symbols to exclude from trading."""
        return [
            # ETFs and other instruments we want to avoid
            "SPY", "QQQ", "IWM", "VIX", "UVXY", "SQQQ", "TQQQ",
            # Add any other symbols you want to exclude
        ]

# Validate configuration on import
TradingConfig.validate_config()
