"""
Unit tests for risk_manager.py - Risk management and position sizing.
"""

import pytest
from datetime import datetime, timedelta
from unittest.mock import patch, MagicMock

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from risk_manager import RiskManager
from config import TradingConfig

class TestRiskManager:
    """Test risk management functionality."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.risk_manager = RiskManager()
        self.initial_equity = 10000.0
    
    def test_initialization(self):
        """Test risk manager initialization."""
        assert self.risk_manager.initial_equity is None
        assert self.risk_manager.current_equity is None
        assert self.risk_manager.trading_halted is False
        assert self.risk_manager.open_positions_count == 0
    
    def test_initialize_equity(self):
        """Test equity initialization."""
        self.risk_manager.initialize_equity(self.initial_equity)
        
        assert self.risk_manager.initial_equity == self.initial_equity
        assert self.risk_manager.daily_start_equity == self.initial_equity
        assert self.risk_manager.peak_equity == self.initial_equity
        assert self.risk_manager.current_equity == self.initial_equity
        assert self.risk_manager.last_equity_check is not None
    
    def test_update_equity_new_high(self):
        """Test equity update with new high water mark."""
        self.risk_manager.initialize_equity(self.initial_equity)
        
        new_equity = 11000.0
        self.risk_manager.update_equity(new_equity)
        
        assert self.risk_manager.current_equity == new_equity
        assert self.risk_manager.peak_equity == new_equity
    
    def test_update_equity_drawdown(self):
        """Test equity update with drawdown."""
        self.risk_manager.initialize_equity(self.initial_equity)
        
        # Simulate drawdown
        new_equity = 9000.0
        self.risk_manager.update_equity(new_equity)
        
        assert self.risk_manager.current_equity == new_equity
        assert self.risk_manager.peak_equity == self.initial_equity  # Should remain at high
    
    def test_daily_drawdown_limit(self):
        """Test daily drawdown limit enforcement."""
        self.risk_manager.initialize_equity(self.initial_equity)
        
        # Simulate daily drawdown exceeding limit (4%)
        daily_loss = self.initial_equity * (TradingConfig.MAX_DAILY_DRAWDOWN + 0.01)
        new_equity = self.initial_equity - daily_loss
        
        self.risk_manager.update_equity(new_equity)
        
        # Should halt trading
        assert not self.risk_manager.check_risk_limits()
        assert self.risk_manager.trading_halted
        assert "daily drawdown" in self.risk_manager.halt_reason.lower()
    
    def test_total_drawdown_limit(self):
        """Test total drawdown limit enforcement."""
        self.risk_manager.initialize_equity(self.initial_equity)
        
        # First, set a higher peak
        self.risk_manager.update_equity(12000.0)
        
        # Then simulate total drawdown exceeding limit (20%)
        total_loss = 12000.0 * (TradingConfig.MAX_TOTAL_DRAWDOWN + 0.01)
        new_equity = 12000.0 - total_loss
        
        self.risk_manager.update_equity(new_equity)
        
        # Should halt trading
        assert not self.risk_manager.check_risk_limits()
        assert self.risk_manager.trading_halted
        assert "total drawdown" in self.risk_manager.halt_reason.lower()
    
    def test_position_count_limit(self):
        """Test maximum position count limit."""
        self.risk_manager.initialize_equity(self.initial_equity)
        
        # Set position count to maximum
        self.risk_manager.update_position_count(TradingConfig.MAX_CONCURRENT_POSITIONS)
        
        # Should block new trades
        assert not self.risk_manager.check_risk_limits()
        assert not self.risk_manager.trading_halted  # Not halted, just at limit
    
    def test_calculate_position_size(self):
        """Test position size calculation."""
        self.risk_manager.initialize_equity(self.initial_equity)
        
        entry_price = 100.0
        stop_loss_price = 95.0  # $5 risk per share
        
        position_size = self.risk_manager.calculate_position_size(entry_price, stop_loss_price)
        
        # Expected: $10,000 * 1.5% = $150 max risk
        # Risk per share = $5, so position size = $150 / $5 = 30 shares
        assert position_size == 30
    
    def test_calculate_position_size_when_halted(self):
        """Test position size calculation when trading is halted."""
        self.risk_manager.initialize_equity(self.initial_equity)
        
        # Force halt trading
        self.risk_manager._halt_trading("Test halt")
        
        position_size = self.risk_manager.calculate_position_size(100.0, 95.0)
        assert position_size == 0
    
    def test_validate_trade_approved(self):
        """Test trade validation for approved trade."""
        self.risk_manager.initialize_equity(self.initial_equity)
        
        validation = self.risk_manager.validate_trade(
            symbol="AAPL",
            entry_price=100.0,
            stop_loss_price=95.0,
            take_profit_price=105.0
        )
        
        assert validation['approved'] is True
        assert validation['position_size'] > 0
        assert validation['risk_amount'] > 0
        assert validation['risk_percentage'] > 0
        assert validation['reason'] == 'Trade approved'
    
    def test_validate_trade_rejected_halted(self):
        """Test trade validation when trading is halted."""
        self.risk_manager.initialize_equity(self.initial_equity)
        
        # Force halt trading
        self.risk_manager._halt_trading("Test halt")
        
        validation = self.risk_manager.validate_trade(
            symbol="AAPL",
            entry_price=100.0,
            stop_loss_price=95.0,
            take_profit_price=105.0
        )
        
        assert validation['approved'] is False
        assert validation['position_size'] == 0
        assert "Test halt" in validation['reason']
    
    def test_validate_trade_invalid_prices(self):
        """Test trade validation with invalid prices."""
        self.risk_manager.initialize_equity(self.initial_equity)
        
        validation = self.risk_manager.validate_trade(
            symbol="AAPL",
            entry_price=0.0,  # Invalid price
            stop_loss_price=95.0,
            take_profit_price=105.0
        )
        
        assert validation['approved'] is False
        assert "Invalid price levels" in validation['reason']
    
    @patch('risk_manager.is_new_trading_day')
    def test_daily_reset(self, mock_new_day):
        """Test daily tracking reset."""
        self.risk_manager.initialize_equity(self.initial_equity)
        
        # Simulate some equity change
        self.risk_manager.update_equity(9500.0)
        
        # Mock new trading day
        mock_new_day.return_value = True
        
        # Update equity (should trigger daily reset)
        self.risk_manager.update_equity(9500.0)
        
        # Daily start equity should be updated
        assert self.risk_manager.daily_start_equity == 9500.0
    
    def test_get_risk_metrics(self):
        """Test risk metrics reporting."""
        self.risk_manager.initialize_equity(self.initial_equity)
        
        # Update with some changes
        self.risk_manager.update_equity(9500.0)  # 5% daily drawdown
        self.risk_manager.update_position_count(5)
        
        metrics = self.risk_manager.get_risk_metrics()
        
        assert 'current_equity' in metrics
        assert 'daily_drawdown' in metrics
        assert 'total_drawdown' in metrics
        assert 'open_positions' in metrics
        assert 'trading_halted' in metrics
        
        assert metrics['current_equity'] == 9500.0
        assert metrics['open_positions'] == 5
        assert metrics['trading_halted'] is False
    
    def test_force_resume_trading(self):
        """Test force resume trading functionality."""
        self.risk_manager.initialize_equity(self.initial_equity)
        
        # Halt trading
        self.risk_manager._halt_trading("Test halt")
        assert self.risk_manager.trading_halted is True
        
        # Force resume
        result = self.risk_manager.force_resume_trading()
        
        assert result is True
        assert self.risk_manager.trading_halted is False
        assert self.risk_manager.halt_reason is None
    
    def test_force_resume_when_not_halted(self):
        """Test force resume when trading is not halted."""
        self.risk_manager.initialize_equity(self.initial_equity)
        
        # Trading is not halted
        result = self.risk_manager.force_resume_trading()
        
        assert result is False
    
    def test_risk_limits_without_initialization(self):
        """Test risk limits check without equity initialization."""
        # Don't initialize equity
        result = self.risk_manager.check_risk_limits()
        
        assert result is False
    
    def test_position_size_without_initialization(self):
        """Test position size calculation without equity initialization."""
        position_size = self.risk_manager.calculate_position_size(100.0, 95.0)
        
        assert position_size == 0
    
    def test_halt_trading_functionality(self):
        """Test halt trading mechanism."""
        self.risk_manager.initialize_equity(self.initial_equity)
        
        # Initially not halted
        assert not self.risk_manager.trading_halted
        assert self.risk_manager.halt_reason is None
        
        # Halt trading
        reason = "Test halt reason"
        self.risk_manager._halt_trading(reason)
        
        assert self.risk_manager.trading_halted
        assert self.risk_manager.halt_reason == reason
        
        # Subsequent halts should not change the reason
        self.risk_manager._halt_trading("New reason")
        assert self.risk_manager.halt_reason == reason  # Should remain original

if __name__ == "__main__":
    pytest.main([__file__])
