"""
Unit tests for utils.py - Technical analysis and utility functions.
"""

import pytest
import pandas as pd
import numpy as np
from datetime import datetime, time
from unittest.mock import patch, MagicMock
from freezegun import freeze_time

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils import (
    is_market_open, calculate_bollinger_bands, calculate_atr,
    calculate_position_size, validate_symbol, calculate_drawdown,
    is_new_trading_day, RateLimiter
)
from config import TradingConfig

class TestMarketHours:
    """Test market hours functionality."""
    
    @freeze_time("2024-01-15 10:30:00")  # Monday 10:30 AM ET
    @patch('utils.datetime')
    def test_market_open_during_hours(self, mock_datetime):
        """Test market is detected as open during trading hours."""
        # Mock ET timezone
        mock_et_time = MagicMock()
        mock_et_time.weekday.return_value = 0  # Monday
        mock_et_time.time.return_value = time(10, 30)  # 10:30 AM
        
        mock_datetime.now.return_value = mock_et_time
        
        with patch('utils.pytz.timezone') as mock_tz:
            mock_tz.return_value.localize.return_value = mock_et_time
            result = is_market_open()
            assert result is True
    
    @freeze_time("2024-01-15 08:00:00")  # Monday 8:00 AM ET (before open)
    @patch('utils.datetime')
    def test_market_closed_before_hours(self, mock_datetime):
        """Test market is detected as closed before trading hours."""
        mock_et_time = MagicMock()
        mock_et_time.weekday.return_value = 0  # Monday
        mock_et_time.time.return_value = time(8, 0)  # 8:00 AM
        
        mock_datetime.now.return_value = mock_et_time
        
        result = is_market_open()
        assert result is False
    
    @freeze_time("2024-01-13 10:30:00")  # Saturday 10:30 AM ET
    @patch('utils.datetime')
    def test_market_closed_weekend(self, mock_datetime):
        """Test market is detected as closed on weekends."""
        mock_et_time = MagicMock()
        mock_et_time.weekday.return_value = 5  # Saturday
        mock_et_time.time.return_value = time(10, 30)  # 10:30 AM
        
        mock_datetime.now.return_value = mock_et_time
        
        result = is_market_open()
        assert result is False

class TestTechnicalIndicators:
    """Test technical analysis calculations."""
    
    def test_bollinger_bands_calculation(self):
        """Test Bollinger Bands calculation with known data."""
        # Create test data
        prices = pd.Series([20, 21, 19, 22, 20, 23, 21, 20, 22, 24, 
                           23, 22, 21, 20, 19, 21, 22, 23, 24, 25])
        
        upper, middle, lower = calculate_bollinger_bands(prices, period=10, std_dev=2.0)
        
        # Verify we get Series back
        assert isinstance(upper, pd.Series)
        assert isinstance(middle, pd.Series)
        assert isinstance(lower, pd.Series)
        
        # Verify lengths match
        assert len(upper) == len(prices)
        assert len(middle) == len(prices)
        assert len(lower) == len(prices)
        
        # Verify middle band is moving average
        expected_middle = prices.rolling(window=10).mean()
        pd.testing.assert_series_equal(middle, expected_middle)
        
        # Verify bands are properly spaced
        last_valid_idx = middle.last_valid_index()
        if last_valid_idx is not None:
            assert upper.iloc[last_valid_idx] > middle.iloc[last_valid_idx]
            assert lower.iloc[last_valid_idx] < middle.iloc[last_valid_idx]
    
    def test_atr_calculation(self):
        """Test ATR calculation with known data."""
        # Create test OHLC data
        high = pd.Series([22, 23, 21, 24, 22, 25, 23, 22, 24, 26])
        low = pd.Series([18, 19, 17, 20, 18, 21, 19, 18, 20, 22])
        close = pd.Series([20, 21, 19, 22, 20, 23, 21, 20, 22, 24])
        
        atr = calculate_atr(high, low, close, period=5)
        
        # Verify we get a Series back
        assert isinstance(atr, pd.Series)
        assert len(atr) == len(high)
        
        # Verify ATR values are positive where calculated
        valid_atr = atr.dropna()
        assert all(valid_atr > 0)
    
    def test_bollinger_bands_insufficient_data(self):
        """Test Bollinger Bands with insufficient data."""
        prices = pd.Series([20, 21, 19])  # Only 3 data points
        
        upper, middle, lower = calculate_bollinger_bands(prices, period=10)
        
        # Should return empty series or series with NaN values
        assert len(upper) == len(prices)
        assert len(middle) == len(prices)
        assert len(lower) == len(prices)

class TestPositionSizing:
    """Test position sizing calculations."""
    
    def test_position_size_calculation(self):
        """Test position size calculation with valid inputs."""
        account_equity = 10000.0
        entry_price = 100.0
        stop_loss_price = 95.0  # $5 risk per share
        max_risk_pct = 0.015  # 1.5%
        
        position_size = calculate_position_size(
            account_equity, entry_price, stop_loss_price, max_risk_pct
        )
        
        # Expected: $10,000 * 1.5% = $150 max risk
        # Risk per share = $5, so position size = $150 / $5 = 30 shares
        assert position_size == 30
    
    def test_position_size_zero_risk(self):
        """Test position size calculation with zero risk per share."""
        position_size = calculate_position_size(10000.0, 100.0, 100.0, 0.015)
        assert position_size == 0
    
    def test_position_size_minimum_one_share(self):
        """Test position size calculation returns minimum 1 share."""
        # Very small account with high-priced stock
        position_size = calculate_position_size(100.0, 1000.0, 950.0, 0.015)
        assert position_size >= 1

class TestUtilityFunctions:
    """Test utility functions."""
    
    def test_validate_symbol_valid(self):
        """Test symbol validation with valid symbols."""
        assert validate_symbol("AAPL") is True
        assert validate_symbol("MSFT") is True
        assert validate_symbol("GOOGL") is True
    
    def test_validate_symbol_invalid(self):
        """Test symbol validation with invalid symbols."""
        assert validate_symbol("") is False
        assert validate_symbol("TOOLONG") is False  # More than 5 characters
        assert validate_symbol("SPY") is False  # In excluded list
    
    def test_calculate_drawdown(self):
        """Test drawdown calculation."""
        # 10% drawdown
        drawdown = calculate_drawdown(9000.0, 10000.0)
        assert drawdown == 0.1
        
        # No drawdown
        drawdown = calculate_drawdown(10000.0, 10000.0)
        assert drawdown == 0.0
        
        # Gain (should return 0)
        drawdown = calculate_drawdown(11000.0, 10000.0)
        assert drawdown == 0.0
    
    def test_is_new_trading_day(self):
        """Test new trading day detection."""
        # First call should return True
        assert is_new_trading_day(None) is True
        
        # Same day should return False
        now = datetime.now()
        assert is_new_trading_day(now) is False

class TestRateLimiter:
    """Test rate limiting functionality."""
    
    def test_rate_limiter_allows_calls(self):
        """Test rate limiter allows calls within limit."""
        limiter = RateLimiter(max_calls=5, time_window=60)
        
        # Should allow first 5 calls
        for _ in range(5):
            assert limiter.can_make_call() is True
            limiter.record_call()
        
        # Should block 6th call
        assert limiter.can_make_call() is False
    
    def test_rate_limiter_resets_after_window(self):
        """Test rate limiter resets after time window."""
        limiter = RateLimiter(max_calls=2, time_window=1)  # 1 second window
        
        # Use up the limit
        assert limiter.can_make_call() is True
        limiter.record_call()
        assert limiter.can_make_call() is True
        limiter.record_call()
        assert limiter.can_make_call() is False
        
        # Wait for window to pass (simulate by clearing old calls)
        limiter.calls = []
        assert limiter.can_make_call() is True

if __name__ == "__main__":
    pytest.main([__file__])
