#!/usr/bin/env python3
"""
Quick test runner to validate core functionality without full pytest setup.
Useful for rapid validation during development.
"""

import sys
import os
import traceback
from datetime import datetime

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """Test that all modules can be imported."""
    print("Testing imports...")
    try:
        import config
        import utils
        import strategy
        import risk_manager
        import order_manager
        import universe_selector
        import main
        print("✅ All imports successful")
        return True
    except Exception as e:
        print(f"❌ Import failed: {e}")
        traceback.print_exc()
        return False

def test_config_validation():
    """Test configuration validation."""
    print("Testing configuration...")
    try:
        from config import TradingConfig
        
        # Test that config has required attributes
        required_attrs = [
            'UNIVERSE_SIZE', 'BB_PERIOD', 'ATR_PERIOD',
            'MAX_POSITION_RISK', 'MAX_DAILY_DRAWDOWN'
        ]
        
        for attr in required_attrs:
            if not hasattr(TradingConfig, attr):
                raise AttributeError(f"Missing config attribute: {attr}")
        
        print("✅ Configuration validation passed")
        return True
    except Exception as e:
        print(f"❌ Configuration test failed: {e}")
        return False

def test_technical_indicators():
    """Test technical indicator calculations."""
    print("Testing technical indicators...")
    try:
        import pandas as pd
        from utils import calculate_bollinger_bands, calculate_atr
        
        # Test Bollinger Bands
        prices = pd.Series([100, 101, 99, 102, 98, 103, 97, 104, 96, 105] * 3)
        upper, middle, lower = calculate_bollinger_bands(prices, period=10)
        
        if len(upper) != len(prices):
            raise ValueError("Bollinger Bands length mismatch")
        
        # Test ATR
        high = pd.Series([102, 103, 101, 104, 100, 105, 99, 106, 98, 107] * 3)
        low = pd.Series([98, 99, 97, 100, 96, 101, 95, 102, 94, 103] * 3)
        close = prices
        
        atr = calculate_atr(high, low, close, period=10)
        
        if len(atr) != len(prices):
            raise ValueError("ATR length mismatch")
        
        print("✅ Technical indicators test passed")
        return True
    except Exception as e:
        print(f"❌ Technical indicators test failed: {e}")
        traceback.print_exc()
        return False

def test_strategy_basic():
    """Test basic strategy functionality."""
    print("Testing strategy...")
    try:
        from strategy import TradingStrategy, SignalType
        from tests.fixtures import MockBar
        
        strategy = TradingStrategy()
        symbol = "AAPL"
        
        # Add some test data
        for i in range(25):  # Enough for BB calculation
            bar = MockBar(symbol, datetime.now(), 100 + i, 101 + i, 99 + i, 100.5 + i, 1000000)
            strategy.add_bar_data(symbol, bar)
        
        # Test signal generation
        signal_type, signal_data = strategy.generate_signal(symbol)
        
        # Should return some signal type (even if NO_SIGNAL)
        if not isinstance(signal_type, SignalType):
            raise ValueError("Invalid signal type returned")
        
        print("✅ Strategy test passed")
        return True
    except Exception as e:
        print(f"❌ Strategy test failed: {e}")
        traceback.print_exc()
        return False

def test_risk_manager_basic():
    """Test basic risk manager functionality."""
    print("Testing risk manager...")
    try:
        from risk_manager import RiskManager
        
        rm = RiskManager()
        rm.initialize_equity(10000.0)
        
        # Test position sizing
        position_size = rm.calculate_position_size(100.0, 95.0)  # $5 risk per share
        
        if position_size <= 0:
            raise ValueError("Position size should be positive")
        
        # Test trade validation
        validation = rm.validate_trade("AAPL", 100.0, 95.0, 105.0)
        
        if not isinstance(validation, dict):
            raise ValueError("Validation should return dict")
        
        if 'approved' not in validation:
            raise ValueError("Validation missing 'approved' key")
        
        print("✅ Risk manager test passed")
        return True
    except Exception as e:
        print(f"❌ Risk manager test failed: {e}")
        traceback.print_exc()
        return False

def test_market_hours():
    """Test market hours detection."""
    print("Testing market hours...")
    try:
        from utils import is_market_open
        
        # This will return True or False based on current time
        result = is_market_open()
        
        if not isinstance(result, bool):
            raise ValueError("Market hours check should return boolean")
        
        print(f"✅ Market hours test passed (market currently {'open' if result else 'closed'})")
        return True
    except Exception as e:
        print(f"❌ Market hours test failed: {e}")
        traceback.print_exc()
        return False

def main():
    """Run all quick tests."""
    print("=" * 60)
    print("QUICK VALIDATION TESTS")
    print("=" * 60)
    
    tests = [
        test_imports,
        test_config_validation,
        test_technical_indicators,
        test_strategy_basic,
        test_risk_manager_basic,
        test_market_hours,
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"❌ Test {test.__name__} crashed: {e}")
            results.append(False)
        print()
    
    # Summary
    print("=" * 60)
    print("SUMMARY")
    print("=" * 60)
    
    passed = sum(results)
    total = len(results)
    
    if passed == total:
        print(f"✅ All quick tests passed! ({passed}/{total})")
        print("\n🚀 Your trading bot is ready for testing!")
        print("\nNext steps:")
        print("1. Install test dependencies: pip install -r requirements.txt")
        print("2. Run full test suite: python run_tests.py")
        print("3. Test with paper trading when market opens")
        return 0
    else:
        print(f"❌ Some tests failed ({passed}/{total} passed)")
        print("\n🔧 Please fix the failing tests before proceeding")
        return 1

if __name__ == "__main__":
    sys.exit(main())
