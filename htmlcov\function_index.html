<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_dca529e9.css" type="text/css">
    <script src="coverage_html_cb_497bf287.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">39%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>n</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button" href="index.html">Files</a>
                <a class="button current">Functions</a>
                <a class="button" href="class_index.html">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.10.5">coverage.py v7.10.5</a>,
            created at 2025-08-23 16:20 +0100
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="region" class="name left" aria-sort="none" data-default-sort-order="ascending" data-shortcut="n">function<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="config_py.html#t65">config.py</a></td>
                <td class="name left"><a href="config_py.html#t65"><data value='validate_config'>TradingConfig.validate_config</data></a></td>
                <td>8</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="6 8">75%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="config_py.html#t83">config.py</a></td>
                <td class="name left"><a href="config_py.html#t83"><data value='get_excluded_symbols'>TradingConfig.get_excluded_symbols</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="config_py.html">config.py</a></td>
                <td class="name left"><a href="config_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>38</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="38 38">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="main_py.html#t64">main.py</a></td>
                <td class="name left"><a href="main_py.html#t64"><data value='init__'>TradingBot.__init__</data></a></td>
                <td>11</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="11 11">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="main_py.html#t93">main.py</a></td>
                <td class="name left"><a href="main_py.html#t93"><data value='initialize'>TradingBot.initialize</data></a></td>
                <td>21</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="16 21">76%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="main_py.html#t140">main.py</a></td>
                <td class="name left"><a href="main_py.html#t140"><data value='on_bar'>TradingBot.on_bar</data></a></td>
                <td>13</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="12 13">92%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="main_py.html#t173">main.py</a></td>
                <td class="name left"><a href="main_py.html#t173"><data value='process_signal'>TradingBot.process_signal</data></a></td>
                <td>8</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="4 8">50%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="main_py.html#t195">main.py</a></td>
                <td class="name left"><a href="main_py.html#t195"><data value='handle_entry_signal'>TradingBot.handle_entry_signal</data></a></td>
                <td>16</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="10 16">62%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="main_py.html#t238">main.py</a></td>
                <td class="name left"><a href="main_py.html#t238"><data value='handle_exit_signal'>TradingBot.handle_exit_signal</data></a></td>
                <td>18</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="main_py.html#t271">main.py</a></td>
                <td class="name left"><a href="main_py.html#t271"><data value='periodic_tasks'>TradingBot.periodic_tasks</data></a></td>
                <td>12</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="10 12">83%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="main_py.html#t294">main.py</a></td>
                <td class="name left"><a href="main_py.html#t294"><data value='update_subscriptions'>TradingBot.update_subscriptions</data></a></td>
                <td>14</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="14 14">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="main_py.html#t316">main.py</a></td>
                <td class="name left"><a href="main_py.html#t316"><data value='log_status'>TradingBot.log_status</data></a></td>
                <td>15</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="13 15">87%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="main_py.html#t336">main.py</a></td>
                <td class="name left"><a href="main_py.html#t336"><data value='run'>TradingBot.run</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="main_py.html#t366">main.py</a></td>
                <td class="name left"><a href="main_py.html#t366"><data value='shutdown'>TradingBot.shutdown</data></a></td>
                <td>9</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="7 9">78%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="main_py.html#t384">main.py</a></td>
                <td class="name left"><a href="main_py.html#t384"><data value='signal_handler'>TradingBot.signal_handler</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="main_py.html#t389">main.py</a></td>
                <td class="name left"><a href="main_py.html#t389"><data value='main'>main</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="main_py.html">main.py</a></td>
                <td class="name left"><a href="main_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>43</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="35 43">81%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="order_manager_py.html#t25">order_manager.py</a></td>
                <td class="name left"><a href="order_manager_py.html#t25"><data value='init__'>OrderManager.__init__</data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="order_manager_py.html#t44">order_manager.py</a></td>
                <td class="name left"><a href="order_manager_py.html#t44"><data value='update_position_cache'>OrderManager.update_position_cache</data></a></td>
                <td>27</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="17 27">63%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="order_manager_py.html#t91">order_manager.py</a></td>
                <td class="name left"><a href="order_manager_py.html#t91"><data value='has_position'>OrderManager.has_position</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="order_manager_py.html#t103">order_manager.py</a></td>
                <td class="name left"><a href="order_manager_py.html#t103"><data value='get_position_count'>OrderManager.get_position_count</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="order_manager_py.html#t107">order_manager.py</a></td>
                <td class="name left"><a href="order_manager_py.html#t107"><data value='submit_bracket_order'>OrderManager.submit_bracket_order</data></a></td>
                <td>20</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="0 20">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="order_manager_py.html#t173">order_manager.py</a></td>
                <td class="name left"><a href="order_manager_py.html#t173"><data value='submit_market_order'>OrderManager.submit_market_order</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="order_manager_py.html#t212">order_manager.py</a></td>
                <td class="name left"><a href="order_manager_py.html#t212"><data value='record_failed_order'>OrderManager._record_failed_order</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="order_manager_py.html#t228">order_manager.py</a></td>
                <td class="name left"><a href="order_manager_py.html#t228"><data value='cancel_all_orders'>OrderManager.cancel_all_orders</data></a></td>
                <td>25</td>
                <td>25</td>
                <td>0</td>
                <td class="right" data-ratio="0 25">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="order_manager_py.html#t274">order_manager.py</a></td>
                <td class="name left"><a href="order_manager_py.html#t274"><data value='liquidate_all_positions'>OrderManager.liquidate_all_positions</data></a></td>
                <td>27</td>
                <td>27</td>
                <td>0</td>
                <td class="right" data-ratio="0 27">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="order_manager_py.html#t321">order_manager.py</a></td>
                <td class="name left"><a href="order_manager_py.html#t321"><data value='get_order_stats'>OrderManager.get_order_stats</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="order_manager_py.html#t343">order_manager.py</a></td>
                <td class="name left"><a href="order_manager_py.html#t343"><data value='clear_pending_order'>OrderManager.clear_pending_order</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="order_manager_py.html#t348">order_manager.py</a></td>
                <td class="name left"><a href="order_manager_py.html#t348"><data value='get_pending_order'>OrderManager.get_pending_order</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="order_manager_py.html">order_manager.py</a></td>
                <td class="name left"><a href="order_manager_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>25</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="25 25">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="risk_manager_py.html#t20">risk_manager.py</a></td>
                <td class="name left"><a href="risk_manager_py.html#t20"><data value='init__'>RiskManager.__init__</data></a></td>
                <td>10</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="10 10">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="risk_manager_py.html#t38">risk_manager.py</a></td>
                <td class="name left"><a href="risk_manager_py.html#t38"><data value='initialize_equity'>RiskManager.initialize_equity</data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="risk_manager_py.html#t54">risk_manager.py</a></td>
                <td class="name left"><a href="risk_manager_py.html#t54"><data value='update_equity'>RiskManager.update_equity</data></a></td>
                <td>7</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="6 7">86%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="risk_manager_py.html#t74">risk_manager.py</a></td>
                <td class="name left"><a href="risk_manager_py.html#t74"><data value='reset_daily_tracking'>RiskManager._reset_daily_tracking</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="risk_manager_py.html#t88">risk_manager.py</a></td>
                <td class="name left"><a href="risk_manager_py.html#t88"><data value='check_risk_limits'>RiskManager.check_risk_limits</data></a></td>
                <td>15</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="7 15">47%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="risk_manager_py.html#t118">risk_manager.py</a></td>
                <td class="name left"><a href="risk_manager_py.html#t118"><data value='halt_trading'>RiskManager._halt_trading</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="risk_manager_py.html#t130">risk_manager.py</a></td>
                <td class="name left"><a href="risk_manager_py.html#t130"><data value='calculate_position_size'>RiskManager.calculate_position_size</data></a></td>
                <td>8</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="5 8">62%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="risk_manager_py.html#t158">risk_manager.py</a></td>
                <td class="name left"><a href="risk_manager_py.html#t158"><data value='validate_trade'>RiskManager.validate_trade</data></a></td>
                <td>20</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="12 20">60%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="risk_manager_py.html#t218">risk_manager.py</a></td>
                <td class="name left"><a href="risk_manager_py.html#t218"><data value='update_position_count'>RiskManager.update_position_count</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="risk_manager_py.html#t227">risk_manager.py</a></td>
                <td class="name left"><a href="risk_manager_py.html#t227"><data value='get_risk_metrics'>RiskManager.get_risk_metrics</data></a></td>
                <td>9</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="8 9">89%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="risk_manager_py.html#t261">risk_manager.py</a></td>
                <td class="name left"><a href="risk_manager_py.html#t261"><data value='force_resume_trading'>RiskManager.force_resume_trading</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="risk_manager_py.html">risk_manager.py</a></td>
                <td class="name left"><a href="risk_manager_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>19</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="19 19">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="run_tests_py.html#t12">run_tests.py</a></td>
                <td class="name left"><a href="run_tests_py.html#t12"><data value='run_command'>run_command</data></a></td>
                <td>20</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="0 20">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="run_tests_py.html#t41">run_tests.py</a></td>
                <td class="name left"><a href="run_tests_py.html#t41"><data value='main'>main</data></a></td>
                <td>49</td>
                <td>49</td>
                <td>0</td>
                <td class="right" data-ratio="0 49">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="run_tests_py.html">run_tests.py</a></td>
                <td class="name left"><a href="run_tests_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="run_tests_simple_py.html#t12">run_tests_simple.py</a></td>
                <td class="name left"><a href="run_tests_simple_py.html#t12"><data value='run_command'>run_command</data></a></td>
                <td>20</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="0 20">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="run_tests_simple_py.html#t41">run_tests_simple.py</a></td>
                <td class="name left"><a href="run_tests_simple_py.html#t41"><data value='main'>main</data></a></td>
                <td>31</td>
                <td>31</td>
                <td>0</td>
                <td class="right" data-ratio="0 31">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="run_tests_simple_py.html">run_tests_simple.py</a></td>
                <td class="name left"><a href="run_tests_simple_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="strategy_py.html#t36">strategy.py</a></td>
                <td class="name left"><a href="strategy_py.html#t36"><data value='init__'>TradingStrategy.__init__</data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="strategy_py.html#t51">strategy.py</a></td>
                <td class="name left"><a href="strategy_py.html#t51"><data value='add_bar_data'>TradingStrategy.add_bar_data</data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="strategy_py.html#t76">strategy.py</a></td>
                <td class="name left"><a href="strategy_py.html#t76"><data value='get_dataframe'>TradingStrategy._get_dataframe</data></a></td>
                <td>9</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="5 9">56%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="strategy_py.html#t97">strategy.py</a></td>
                <td class="name left"><a href="strategy_py.html#t97"><data value='calculate_indicators'>TradingStrategy.calculate_indicators</data></a></td>
                <td>15</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="9 15">60%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="strategy_py.html#t142">strategy.py</a></td>
                <td class="name left"><a href="strategy_py.html#t142"><data value='generate_signal'>TradingStrategy.generate_signal</data></a></td>
                <td>30</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="17 30">57%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="strategy_py.html#t214">strategy.py</a></td>
                <td class="name left"><a href="strategy_py.html#t214"><data value='update_position'>TradingStrategy.update_position</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="strategy_py.html#t228">strategy.py</a></td>
                <td class="name left"><a href="strategy_py.html#t228"><data value='get_position'>TradingStrategy.get_position</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="strategy_py.html#t240">strategy.py</a></td>
                <td class="name left"><a href="strategy_py.html#t240"><data value='calculate_trade_levels'>TradingStrategy.calculate_trade_levels</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="strategy_py.html#t276">strategy.py</a></td>
                <td class="name left"><a href="strategy_py.html#t276"><data value='get_strategy_stats'>TradingStrategy.get_strategy_stats</data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="strategy_py.html#t299">strategy.py</a></td>
                <td class="name left"><a href="strategy_py.html#t299"><data value='clear_symbol_data'>TradingStrategy.clear_symbol_data</data></a></td>
                <td>5</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="3 5">60%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="strategy_py.html#t312">strategy.py</a></td>
                <td class="name left"><a href="strategy_py.html#t312"><data value='has_sufficient_data'>TradingStrategy.has_sufficient_data</data></a></td>
                <td>4</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="3 4">75%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="strategy_py.html">strategy.py</a></td>
                <td class="name left"><a href="strategy_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>31</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="31 31">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="test_quick_py.html#t15">test_quick.py</a></td>
                <td class="name left"><a href="test_quick_py.html#t15"><data value='test_imports'>test_imports</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="test_quick_py.html#t33">test_quick.py</a></td>
                <td class="name left"><a href="test_quick_py.html#t33"><data value='test_config_validation'>test_config_validation</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="test_quick_py.html#t55">test_quick.py</a></td>
                <td class="name left"><a href="test_quick_py.html#t55"><data value='test_technical_indicators'>test_technical_indicators</data></a></td>
                <td>20</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="0 20">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="test_quick_py.html#t86">test_quick.py</a></td>
                <td class="name left"><a href="test_quick_py.html#t86"><data value='test_strategy_basic'>test_strategy_basic</data></a></td>
                <td>18</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="test_quick_py.html#t115">test_quick.py</a></td>
                <td class="name left"><a href="test_quick_py.html#t115"><data value='test_risk_manager_basic'>test_risk_manager_basic</data></a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="test_quick_py.html#t146">test_quick.py</a></td>
                <td class="name left"><a href="test_quick_py.html#t146"><data value='test_market_hours'>test_market_hours</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="test_quick_py.html#t165">test_quick.py</a></td>
                <td class="name left"><a href="test_quick_py.html#t165"><data value='main'>main</data></a></td>
                <td>29</td>
                <td>29</td>
                <td>0</td>
                <td class="right" data-ratio="0 29">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="test_quick_py.html">test_quick.py</a></td>
                <td class="name left"><a href="test_quick_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531___init___py.html">tests\__init__.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_conftest_py.html#t15">tests\conftest.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_conftest_py.html#t15"><data value='event_loop'>event_loop</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_conftest_py.html">tests\conftest.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_conftest_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>12</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="12 12">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_fixtures_py.html#t16">tests\fixtures.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_fixtures_py.html#t16"><data value='init__'>MockBar.__init__</data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_fixtures_py.html#t29">tests\fixtures.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_fixtures_py.html#t29"><data value='init__'>MockPosition.__init__</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_fixtures_py.html#t37">tests\fixtures.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_fixtures_py.html#t37"><data value='init__'>MockAccount.__init__</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_fixtures_py.html#t45">tests\fixtures.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_fixtures_py.html#t45"><data value='init__'>MockOrder.__init__</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_fixtures_py.html#t55">tests\fixtures.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_fixtures_py.html#t55"><data value='init__'>MockAsset.__init__</data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_fixtures_py.html#t65">tests\fixtures.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_fixtures_py.html#t65"><data value='mock_trading_client'>mock_trading_client</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_fixtures_py.html#t90">tests\fixtures.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_fixtures_py.html#t90"><data value='mock_data_client'>mock_data_client</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_fixtures_py.html#t108">tests\fixtures.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_fixtures_py.html#t108"><data value='sample_price_data'>sample_price_data</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_fixtures_py.html#t124">tests\fixtures.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_fixtures_py.html#t124"><data value='bollinger_bands_test_data'>bollinger_bands_test_data</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_fixtures_py.html#t140">tests\fixtures.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_fixtures_py.html#t140"><data value='high_volatility_data'>high_volatility_data</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_fixtures_py.html#t168">tests\fixtures.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_fixtures_py.html#t168"><data value='mock_websocket_stream'>mock_websocket_stream</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_fixtures_py.html#t177">tests\fixtures.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_fixtures_py.html#t177"><data value='create_test_bars'>create_test_bars</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_fixtures_py.html#t199">tests\fixtures.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_fixtures_py.html#t199"><data value='create_realistic_ohlcv_data'>create_realistic_ohlcv_data</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_fixtures_py.html#t243">tests\fixtures.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_fixtures_py.html#t243"><data value='init__'>MockRateLimiter.__init__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_fixtures_py.html#t246">tests\fixtures.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_fixtures_py.html#t246"><data value='can_make_call'>MockRateLimiter.can_make_call</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_fixtures_py.html#t249">tests\fixtures.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_fixtures_py.html#t249"><data value='record_call'>MockRateLimiter.record_call</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_fixtures_py.html#t281">tests\fixtures.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_fixtures_py.html#t281"><data value='market_scenario'>market_scenario</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_fixtures_py.html">tests\fixtures.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_fixtures_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>37</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="37 37">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_integration_py.html#t27">tests\test_integration.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_integration_py.html#t27"><data value='setup_method'>TestTradingWorkflow.setup_method</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_integration_py.html#t36">tests\test_integration.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_integration_py.html#t36"><data value='test_complete_long_trade_workflow'>TestTradingWorkflow.test_complete_long_trade_workflow</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_integration_py.html#t80">tests\test_integration.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_integration_py.html#t80"><data value='test_complete_short_trade_workflow'>TestTradingWorkflow.test_complete_short_trade_workflow</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_integration_py.html#t123">tests\test_integration.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_integration_py.html#t123"><data value='test_risk_management_blocks_oversized_trade'>TestTradingWorkflow.test_risk_management_blocks_oversized_trade</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_integration_py.html#t145">tests\test_integration.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_integration_py.html#t145"><data value='test_drawdown_halts_trading'>TestTradingWorkflow.test_drawdown_halts_trading</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_integration_py.html#t163">tests\test_integration.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_integration_py.html#t163"><data value='test_position_limit_enforcement'>TestTradingWorkflow.test_position_limit_enforcement</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_integration_py.html#t183">tests\test_integration.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_integration_py.html#t183"><data value='setup_method'>TestOrderManagerIntegration.setup_method</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_integration_py.html#t188">tests\test_integration.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_integration_py.html#t188"><data value='test_position_cache_update'>TestOrderManagerIntegration.test_position_cache_update</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_integration_py.html#t208">tests\test_integration.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_integration_py.html#t208"><data value='test_bracket_order_submission'>TestOrderManagerIntegration.test_bracket_order_submission</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_integration_py.html#t232">tests\test_integration.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_integration_py.html#t232"><data value='test_order_failure_handling'>TestOrderManagerIntegration.test_order_failure_handling</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_integration_py.html#t256">tests\test_integration.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_integration_py.html#t256"><data value='setup_method'>TestUniverseSelectionIntegration.setup_method</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_integration_py.html#t265">tests\test_integration.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_integration_py.html#t265"><data value='test_universe_selection_workflow'>TestUniverseSelectionIntegration.test_universe_selection_workflow</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_integration_py.html#t306">tests\test_integration.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_integration_py.html#t306"><data value='setup_method'>TestEndToEndScenarios.setup_method</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_integration_py.html#t316">tests\test_integration.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_integration_py.html#t316"><data value='test_multiple_symbol_trading'>TestEndToEndScenarios.test_multiple_symbol_trading</data></a></td>
                <td>24</td>
                <td>24</td>
                <td>0</td>
                <td class="right" data-ratio="0 24">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_integration_py.html#t368">tests\test_integration.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_integration_py.html#t368"><data value='test_risk_management_across_portfolio'>TestEndToEndScenarios.test_risk_management_across_portfolio</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_integration_py.html">tests\test_integration.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_integration_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>35</td>
                <td>35</td>
                <td>0</td>
                <td class="right" data-ratio="0 35">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_risk_manager_py.html#t19">tests\test_risk_manager.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_risk_manager_py.html#t19"><data value='setup_method'>TestRiskManager.setup_method</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_risk_manager_py.html#t24">tests\test_risk_manager.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_risk_manager_py.html#t24"><data value='test_initialization'>TestRiskManager.test_initialization</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_risk_manager_py.html#t31">tests\test_risk_manager.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_risk_manager_py.html#t31"><data value='test_initialize_equity'>TestRiskManager.test_initialize_equity</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_risk_manager_py.html#t41">tests\test_risk_manager.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_risk_manager_py.html#t41"><data value='test_update_equity_new_high'>TestRiskManager.test_update_equity_new_high</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_risk_manager_py.html#t51">tests\test_risk_manager.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_risk_manager_py.html#t51"><data value='test_update_equity_drawdown'>TestRiskManager.test_update_equity_drawdown</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_risk_manager_py.html#t62">tests\test_risk_manager.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_risk_manager_py.html#t62"><data value='test_daily_drawdown_limit'>TestRiskManager.test_daily_drawdown_limit</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_risk_manager_py.html#t77">tests\test_risk_manager.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_risk_manager_py.html#t77"><data value='test_total_drawdown_limit'>TestRiskManager.test_total_drawdown_limit</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_risk_manager_py.html#t95">tests\test_risk_manager.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_risk_manager_py.html#t95"><data value='test_position_count_limit'>TestRiskManager.test_position_count_limit</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_risk_manager_py.html#t106">tests\test_risk_manager.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_risk_manager_py.html#t106"><data value='test_calculate_position_size'>TestRiskManager.test_calculate_position_size</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_risk_manager_py.html#t119">tests\test_risk_manager.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_risk_manager_py.html#t119"><data value='test_calculate_position_size_when_halted'>TestRiskManager.test_calculate_position_size_when_halted</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_risk_manager_py.html#t129">tests\test_risk_manager.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_risk_manager_py.html#t129"><data value='test_validate_trade_approved'>TestRiskManager.test_validate_trade_approved</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_risk_manager_py.html#t146">tests\test_risk_manager.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_risk_manager_py.html#t146"><data value='test_validate_trade_rejected_halted'>TestRiskManager.test_validate_trade_rejected_halted</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_risk_manager_py.html#t164">tests\test_risk_manager.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_risk_manager_py.html#t164"><data value='test_validate_trade_invalid_prices'>TestRiskManager.test_validate_trade_invalid_prices</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_risk_manager_py.html#t179">tests\test_risk_manager.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_risk_manager_py.html#t179"><data value='test_daily_reset'>TestRiskManager.test_daily_reset</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_risk_manager_py.html#t195">tests\test_risk_manager.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_risk_manager_py.html#t195"><data value='test_get_risk_metrics'>TestRiskManager.test_get_risk_metrics</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_risk_manager_py.html#t215">tests\test_risk_manager.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_risk_manager_py.html#t215"><data value='test_force_resume_trading'>TestRiskManager.test_force_resume_trading</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_risk_manager_py.html#t230">tests\test_risk_manager.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_risk_manager_py.html#t230"><data value='test_force_resume_when_not_halted'>TestRiskManager.test_force_resume_when_not_halted</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_risk_manager_py.html#t239">tests\test_risk_manager.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_risk_manager_py.html#t239"><data value='test_risk_limits_without_initialization'>TestRiskManager.test_risk_limits_without_initialization</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_risk_manager_py.html#t246">tests\test_risk_manager.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_risk_manager_py.html#t246"><data value='test_position_size_without_initialization'>TestRiskManager.test_position_size_without_initialization</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_risk_manager_py.html#t252">tests\test_risk_manager.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_risk_manager_py.html#t252"><data value='test_halt_trading_functionality'>TestRiskManager.test_halt_trading_functionality</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_risk_manager_py.html">tests\test_risk_manager.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_risk_manager_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>32</td>
                <td>32</td>
                <td>0</td>
                <td class="right" data-ratio="0 32">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_strategy_py.html#t21">tests\test_strategy.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_strategy_py.html#t21"><data value='setup_method'>TestTradingStrategy.setup_method</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_strategy_py.html#t26">tests\test_strategy.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_strategy_py.html#t26"><data value='create_mock_bar'>TestTradingStrategy.create_mock_bar</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_strategy_py.html#t41">tests\test_strategy.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_strategy_py.html#t41"><data value='add_test_bars'>TestTradingStrategy.add_test_bars</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_strategy_py.html#t56">tests\test_strategy.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_strategy_py.html#t56"><data value='test_add_bar_data'>TestTradingStrategy.test_add_bar_data</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_strategy_py.html#t69">tests\test_strategy.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_strategy_py.html#t69"><data value='test_insufficient_data_for_indicators'>TestTradingStrategy.test_insufficient_data_for_indicators</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_strategy_py.html#t79">tests\test_strategy.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_strategy_py.html#t79"><data value='test_sufficient_data_for_indicators'>TestTradingStrategy.test_sufficient_data_for_indicators</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_strategy_py.html#t95">tests\test_strategy.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_strategy_py.html#t95"><data value='test_long_entry_signal_generation'>TestTradingStrategy.test_long_entry_signal_generation</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_strategy_py.html#t112">tests\test_strategy.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_strategy_py.html#t112"><data value='test_short_entry_signal_generation'>TestTradingStrategy.test_short_entry_signal_generation</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_strategy_py.html#t129">tests\test_strategy.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_strategy_py.html#t129"><data value='test_no_signal_with_existing_position'>TestTradingStrategy.test_no_signal_with_existing_position</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_strategy_py.html#t143">tests\test_strategy.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_strategy_py.html#t143"><data value='test_long_exit_signal_generation'>TestTradingStrategy.test_long_exit_signal_generation</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_strategy_py.html#t158">tests\test_strategy.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_strategy_py.html#t158"><data value='test_short_exit_signal_generation'>TestTradingStrategy.test_short_exit_signal_generation</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_strategy_py.html#t173">tests\test_strategy.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_strategy_py.html#t173"><data value='test_position_tracking'>TestTradingStrategy.test_position_tracking</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_strategy_py.html#t190">tests\test_strategy.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_strategy_py.html#t190"><data value='test_calculate_trade_levels'>TestTradingStrategy.test_calculate_trade_levels</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_strategy_py.html#t213">tests\test_strategy.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_strategy_py.html#t213"><data value='test_strategy_stats'>TestTradingStrategy.test_strategy_stats</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_strategy_py.html#t235">tests\test_strategy.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_strategy_py.html#t235"><data value='test_clear_symbol_data'>TestTradingStrategy.test_clear_symbol_data</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_strategy_py.html#t252">tests\test_strategy.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_strategy_py.html#t252"><data value='test_dataframe_conversion'>TestTradingStrategy.test_dataframe_conversion</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_strategy_py.html">tests\test_strategy.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_strategy_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>29</td>
                <td>29</td>
                <td>0</td>
                <td class="right" data-ratio="0 29">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_system_py.html#t24">tests\test_system.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_system_py.html#t24"><data value='setup_method'>TestTradingBotSystem.setup_method</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_system_py.html#t28">tests\test_system.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_system_py.html#t28"><data value='teardown_method'>TestTradingBotSystem.teardown_method</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_system_py.html#t36">tests\test_system.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_system_py.html#t36"><data value='test_bot_initialization'>TestTradingBotSystem.test_bot_initialization</data></a></td>
                <td>18</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="18 18">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_system_py.html#t77">tests\test_system.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_system_py.html#t77"><data value='test_market_closed_behavior'>TestTradingBotSystem.test_market_closed_behavior</data></a></td>
                <td>14</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="14 14">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_system_py.html#t111">tests\test_system.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_system_py.html#t111"><data value='test_on_bar_processing'>TestTradingBotSystem.test_on_bar_processing</data></a></td>
                <td>10</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="10 10">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_system_py.html#t134">tests\test_system.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_system_py.html#t134"><data value='test_signal_processing_workflow'>TestTradingBotSystem.test_signal_processing_workflow</data></a></td>
                <td>16</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="16 16">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_system_py.html#t167">tests\test_system.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_system_py.html#t167"><data value='test_periodic_tasks_execution'>TestTradingBotSystem.test_periodic_tasks_execution</data></a></td>
                <td>16</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="16 16">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_system_py.html#t198">tests\test_system.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_system_py.html#t198"><data value='test_universe_update_and_subscription_changes'>TestTradingBotSystem.test_universe_update_and_subscription_changes</data></a></td>
                <td>12</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="12 12">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_system_py.html#t227">tests\test_system.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_system_py.html#t227"><data value='test_graceful_shutdown'>TestTradingBotSystem.test_graceful_shutdown</data></a></td>
                <td>10</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="10 10">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_system_py.html#t248">tests\test_system.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_system_py.html#t248"><data value='test_error_handling_in_bar_processing'>TestTradingBotSystem.test_error_handling_in_bar_processing</data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_system_py.html#t267">tests\test_system.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_system_py.html#t267"><data value='test_risk_management_integration'>TestTradingBotSystem.test_risk_management_integration</data></a></td>
                <td>14</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="14 14">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_system_py.html#t304">tests\test_system.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_system_py.html#t304"><data value='test_api_error_recovery'>TestSystemResilience.test_api_error_recovery</data></a></td>
                <td>11</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="11 11">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_system_py.html#t326">tests\test_system.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_system_py.html#t326"><data value='test_websocket_reconnection_simulation'>TestSystemResilience.test_websocket_reconnection_simulation</data></a></td>
                <td>11</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="9 11">82%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_system_py.html#t350">tests\test_system.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_system_py.html#t350"><data value='test_memory_management'>TestSystemResilience.test_memory_management</data></a></td>
                <td>10</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="10 10">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_system_py.html">tests\test_system.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_system_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>37</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="36 37">97%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_utils_py.html#t28">tests\test_utils.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_utils_py.html#t28"><data value='test_market_open_during_hours'>TestMarketHours.test_market_open_during_hours</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_utils_py.html#t44">tests\test_utils.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_utils_py.html#t44"><data value='test_market_closed_before_hours'>TestMarketHours.test_market_closed_before_hours</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_utils_py.html#t57">tests\test_utils.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_utils_py.html#t57"><data value='test_market_closed_weekend'>TestMarketHours.test_market_closed_weekend</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_utils_py.html#t71">tests\test_utils.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_utils_py.html#t71"><data value='test_bollinger_bands_calculation'>TestTechnicalIndicators.test_bollinger_bands_calculation</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_utils_py.html#t99">tests\test_utils.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_utils_py.html#t99"><data value='test_atr_calculation'>TestTechnicalIndicators.test_atr_calculation</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_utils_py.html#t116">tests\test_utils.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_utils_py.html#t116"><data value='test_bollinger_bands_insufficient_data'>TestTechnicalIndicators.test_bollinger_bands_insufficient_data</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_utils_py.html#t130">tests\test_utils.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_utils_py.html#t130"><data value='test_position_size_calculation'>TestPositionSizing.test_position_size_calculation</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_utils_py.html#t145">tests\test_utils.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_utils_py.html#t145"><data value='test_position_size_zero_risk'>TestPositionSizing.test_position_size_zero_risk</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_utils_py.html#t150">tests\test_utils.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_utils_py.html#t150"><data value='test_position_size_minimum_one_share'>TestPositionSizing.test_position_size_minimum_one_share</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_utils_py.html#t159">tests\test_utils.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_utils_py.html#t159"><data value='test_validate_symbol_valid'>TestUtilityFunctions.test_validate_symbol_valid</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_utils_py.html#t165">tests\test_utils.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_utils_py.html#t165"><data value='test_validate_symbol_invalid'>TestUtilityFunctions.test_validate_symbol_invalid</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_utils_py.html#t171">tests\test_utils.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_utils_py.html#t171"><data value='test_calculate_drawdown'>TestUtilityFunctions.test_calculate_drawdown</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_utils_py.html#t185">tests\test_utils.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_utils_py.html#t185"><data value='test_is_new_trading_day'>TestUtilityFunctions.test_is_new_trading_day</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_utils_py.html#t197">tests\test_utils.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_utils_py.html#t197"><data value='test_rate_limiter_allows_calls'>TestRateLimiter.test_rate_limiter_allows_calls</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_utils_py.html#t209">tests\test_utils.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_utils_py.html#t209"><data value='test_rate_limiter_resets_after_window'>TestRateLimiter.test_rate_limiter_resets_after_window</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_utils_py.html">tests\test_utils.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_utils_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>39</td>
                <td>39</td>
                <td>0</td>
                <td class="right" data-ratio="0 39">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="universe_selector_py.html#t27">universe_selector.py</a></td>
                <td class="name left"><a href="universe_selector_py.html#t27"><data value='init__'>UniverseSelector.__init__</data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="universe_selector_py.html#t37">universe_selector.py</a></td>
                <td class="name left"><a href="universe_selector_py.html#t37"><data value='get_tradable_assets'>UniverseSelector.get_tradable_assets</data></a></td>
                <td>16</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="11 16">69%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="universe_selector_py.html#t80">universe_selector.py</a></td>
                <td class="name left"><a href="universe_selector_py.html#t80"><data value='calculate_liquidity_metrics'>UniverseSelector.calculate_liquidity_metrics</data></a></td>
                <td>33</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="25 33">76%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="universe_selector_py.html#t148">universe_selector.py</a></td>
                <td class="name left"><a href="universe_selector_py.html#t148"><data value='select_universe'>UniverseSelector.select_universe</data></a></td>
                <td>28</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="22 28">79%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="universe_selector_py.html#t207">universe_selector.py</a></td>
                <td class="name left"><a href="universe_selector_py.html#t207"><data value='update_universe'>UniverseSelector.update_universe</data></a></td>
                <td>23</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="18 23">78%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="universe_selector_py.html#t246">universe_selector.py</a></td>
                <td class="name left"><a href="universe_selector_py.html#t246"><data value='get_current_universe'>UniverseSelector.get_current_universe</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="universe_selector_py.html#t250">universe_selector.py</a></td>
                <td class="name left"><a href="universe_selector_py.html#t250"><data value='is_in_universe'>UniverseSelector.is_in_universe</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="universe_selector_py.html">universe_selector.py</a></td>
                <td class="name left"><a href="universe_selector_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>22</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="22 22">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="utils_py.html#t16">utils.py</a></td>
                <td class="name left"><a href="utils_py.html#t16"><data value='is_market_open'>is_market_open</data></a></td>
                <td>12</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="5 12">42%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="utils_py.html#t43">utils.py</a></td>
                <td class="name left"><a href="utils_py.html#t43"><data value='calculate_bollinger_bands'>calculate_bollinger_bands</data></a></td>
                <td>9</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="6 9">67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="utils_py.html#t68">utils.py</a></td>
                <td class="name left"><a href="utils_py.html#t68"><data value='calculate_atr'>calculate_atr</data></a></td>
                <td>10</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="7 10">70%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="utils_py.html#t99">utils.py</a></td>
                <td class="name left"><a href="utils_py.html#t99"><data value='calculate_position_size'>calculate_position_size</data></a></td>
                <td>11</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="6 11">55%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="utils_py.html#t134">utils.py</a></td>
                <td class="name left"><a href="utils_py.html#t134"><data value='format_currency'>format_currency</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="utils_py.html#t138">utils.py</a></td>
                <td class="name left"><a href="utils_py.html#t138"><data value='format_percentage'>format_percentage</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="utils_py.html#t142">utils.py</a></td>
                <td class="name left"><a href="utils_py.html#t142"><data value='validate_symbol'>validate_symbol</data></a></td>
                <td>5</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="3 5">60%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="utils_py.html#t161">utils.py</a></td>
                <td class="name left"><a href="utils_py.html#t161"><data value='calculate_drawdown'>calculate_drawdown</data></a></td>
                <td>3</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="2 3">67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="utils_py.html#t177">utils.py</a></td>
                <td class="name left"><a href="utils_py.html#t177"><data value='is_new_trading_day'>is_new_trading_day</data></a></td>
                <td>6</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="5 6">83%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="utils_py.html#t200">utils.py</a></td>
                <td class="name left"><a href="utils_py.html#t200"><data value='init__'>RateLimiter.__init__</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="utils_py.html#t205">utils.py</a></td>
                <td class="name left"><a href="utils_py.html#t205"><data value='can_make_call'>RateLimiter.can_make_call</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="utils_py.html#t215">utils.py</a></td>
                <td class="name left"><a href="utils_py.html#t215"><data value='record_call'>RateLimiter.record_call</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="utils_py.html">utils.py</a></td>
                <td class="name left"><a href="utils_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>21</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="21 21">100%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td class="name left">&nbsp;</td>
                <td>2071</td>
                <td>1258</td>
                <td>0</td>
                <td class="right" data-ratio="813 2071">39%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.10.5">coverage.py v7.10.5</a>,
            created at 2025-08-23 16:20 +0100
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href=""></a>
        <a id="nextFileLink" class="nav" href=""></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
