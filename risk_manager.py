"""
Risk management framework with volatility-adjusted position sizing,
portfolio safeguards, and drawdown monitoring.
"""

import logging
from datetime import datetime
from typing import Optional, Dict, Any
import pandas as pd
from config import TradingConfig
from utils import calculate_position_size, calculate_drawdown, is_new_trading_day, format_currency, format_percentage

logger = logging.getLogger(__name__)

class RiskManager:
    """
    Comprehensive risk management system for the trading bot.
    """
    
    def __init__(self):
        # Portfolio tracking
        self.initial_equity: Optional[float] = None
        self.daily_start_equity: Optional[float] = None
        self.peak_equity: Optional[float] = None
        self.current_equity: Optional[float] = None
        
        # Risk state tracking
        self.trading_halted: bool = False
        self.halt_reason: Optional[str] = None
        self.last_equity_check: Optional[datetime] = None
        self.daily_reset_time: Optional[datetime] = None
        
        # Position tracking
        self.open_positions_count: int = 0
        
        logger.info("Risk Manager initialized")
    
    def initialize_equity(self, account_equity: float) -> None:
        """
        Initialize equity tracking with current account value.
        
        Args:
            account_equity: Current account equity value
        """
        self.initial_equity = account_equity
        self.daily_start_equity = account_equity
        self.peak_equity = account_equity
        self.current_equity = account_equity
        self.last_equity_check = datetime.now()
        self.daily_reset_time = datetime.now()
        
        logger.info(f"Risk Manager initialized with equity: {format_currency(account_equity)}")
    
    def update_equity(self, account_equity: float) -> None:
        """
        Update current equity and check for new trading day.
        
        Args:
            account_equity: Current account equity value
        """
        self.current_equity = account_equity
        
        # Update peak equity (high water mark)
        if self.peak_equity is None or account_equity > self.peak_equity:
            self.peak_equity = account_equity
            logger.info(f"New equity high water mark: {format_currency(account_equity)}")
        
        # Check if it's a new trading day
        if is_new_trading_day(self.daily_reset_time):
            self._reset_daily_tracking()
        
        self.last_equity_check = datetime.now()
    
    def _reset_daily_tracking(self) -> None:
        """Reset daily tracking variables for a new trading day."""
        if self.current_equity is not None:
            self.daily_start_equity = self.current_equity
            self.daily_reset_time = datetime.now()
            
            # Reset trading halt if it was due to daily drawdown
            if self.trading_halted and "daily drawdown" in (self.halt_reason or "").lower():
                self.trading_halted = False
                self.halt_reason = None
                logger.info("Trading resumed - new trading day started")
            
            logger.info(f"New trading day started. Daily start equity: {format_currency(self.current_equity)}")
    
    def check_risk_limits(self) -> bool:
        """
        Check all risk limits and halt trading if necessary.
        
        Returns:
            bool: True if trading is allowed, False if halted
        """
        if self.current_equity is None or self.daily_start_equity is None or self.peak_equity is None:
            logger.warning("Equity not initialized, halting trading")
            return False
        
        # Check daily drawdown limit
        daily_drawdown = calculate_drawdown(self.current_equity, self.daily_start_equity)
        if daily_drawdown > TradingConfig.MAX_DAILY_DRAWDOWN:
            self._halt_trading(f"Daily drawdown limit exceeded: {format_percentage(daily_drawdown)} > {format_percentage(TradingConfig.MAX_DAILY_DRAWDOWN)}")
            return False
        
        # Check total drawdown limit
        total_drawdown = calculate_drawdown(self.current_equity, self.peak_equity)
        if total_drawdown > TradingConfig.MAX_TOTAL_DRAWDOWN:
            self._halt_trading(f"Total drawdown limit exceeded: {format_percentage(total_drawdown)} > {format_percentage(TradingConfig.MAX_TOTAL_DRAWDOWN)}")
            return False
        
        # Check maximum concurrent positions
        if self.open_positions_count >= TradingConfig.MAX_CONCURRENT_POSITIONS:
            logger.info(f"Maximum concurrent positions reached: {self.open_positions_count}")
            return False
        
        return not self.trading_halted
    
    def _halt_trading(self, reason: str) -> None:
        """
        Halt all trading with the given reason.
        
        Args:
            reason: Reason for halting trading
        """
        if not self.trading_halted:
            self.trading_halted = True
            self.halt_reason = reason
            logger.critical(f"TRADING HALTED: {reason}")
    
    def calculate_position_size(self, entry_price: float, stop_loss_price: float) -> int:
        """
        Calculate appropriate position size based on risk management rules.
        
        Args:
            entry_price: Planned entry price
            stop_loss_price: Planned stop loss price
            
        Returns:
            Number of shares to trade (0 if risk limits exceeded)
        """
        if not self.check_risk_limits():
            return 0
        
        if self.current_equity is None:
            logger.error("Current equity not set, cannot calculate position size")
            return 0
        
        position_size = calculate_position_size(
            self.current_equity,
            entry_price,
            stop_loss_price,
            TradingConfig.MAX_POSITION_RISK
        )
        
        logger.debug(f"Calculated position size: {position_size} shares for entry ${entry_price:.2f}, stop ${stop_loss_price:.2f}")
        return position_size
    
    def validate_trade(self, symbol: str, entry_price: float, stop_loss_price: float, 
                      take_profit_price: float) -> Dict[str, Any]:
        """
        Validate a potential trade against all risk criteria.
        
        Args:
            symbol: Stock symbol
            entry_price: Planned entry price
            stop_loss_price: Planned stop loss price
            take_profit_price: Planned take profit price
            
        Returns:
            Dictionary with validation results
        """
        validation_result = {
            'approved': False,
            'position_size': 0,
            'reason': '',
            'risk_amount': 0.0,
            'risk_percentage': 0.0
        }
        
        # Check if trading is halted
        if not self.check_risk_limits():
            validation_result['reason'] = self.halt_reason or "Trading halted due to risk limits"
            return validation_result
        
        # Calculate position size
        position_size = self.calculate_position_size(entry_price, stop_loss_price)
        if position_size <= 0:
            validation_result['reason'] = "Position size calculation resulted in 0 shares"
            return validation_result
        
        # Calculate risk metrics
        risk_per_share = abs(entry_price - stop_loss_price)
        total_risk = risk_per_share * position_size
        risk_percentage = total_risk / self.current_equity if self.current_equity else 0
        
        # Validate risk percentage
        if risk_percentage > TradingConfig.MAX_POSITION_RISK:
            validation_result['reason'] = f"Risk percentage too high: {format_percentage(risk_percentage)}"
            return validation_result
        
        # Validate price levels make sense
        if entry_price <= 0 or stop_loss_price <= 0 or take_profit_price <= 0:
            validation_result['reason'] = "Invalid price levels"
            return validation_result
        
        # All validations passed
        validation_result.update({
            'approved': True,
            'position_size': position_size,
            'reason': 'Trade approved',
            'risk_amount': total_risk,
            'risk_percentage': risk_percentage
        })
        
        logger.info(f"Trade validated for {symbol}: {position_size} shares, risk {format_currency(total_risk)} ({format_percentage(risk_percentage)})")
        return validation_result
    
    def update_position_count(self, count: int) -> None:
        """
        Update the count of open positions.
        
        Args:
            count: Current number of open positions
        """
        self.open_positions_count = count
    
    def get_risk_metrics(self) -> Dict[str, Any]:
        """
        Get current risk metrics for monitoring.
        
        Returns:
            Dictionary with current risk metrics
        """
        if self.current_equity is None:
            return {'error': 'Equity not initialized'}
        
        daily_drawdown = 0.0
        total_drawdown = 0.0
        
        if self.daily_start_equity:
            daily_drawdown = calculate_drawdown(self.current_equity, self.daily_start_equity)
        
        if self.peak_equity:
            total_drawdown = calculate_drawdown(self.current_equity, self.peak_equity)
        
        return {
            'current_equity': self.current_equity,
            'daily_start_equity': self.daily_start_equity,
            'peak_equity': self.peak_equity,
            'daily_drawdown': daily_drawdown,
            'total_drawdown': total_drawdown,
            'daily_drawdown_pct': format_percentage(daily_drawdown),
            'total_drawdown_pct': format_percentage(total_drawdown),
            'open_positions': self.open_positions_count,
            'trading_halted': self.trading_halted,
            'halt_reason': self.halt_reason,
            'max_daily_dd_limit': format_percentage(TradingConfig.MAX_DAILY_DRAWDOWN),
            'max_total_dd_limit': format_percentage(TradingConfig.MAX_TOTAL_DRAWDOWN)
        }
    
    def force_resume_trading(self) -> bool:
        """
        Force resume trading (use with caution).
        
        Returns:
            bool: True if trading was resumed
        """
        if self.trading_halted:
            self.trading_halted = False
            old_reason = self.halt_reason
            self.halt_reason = None
            logger.warning(f"Trading forcibly resumed. Previous halt reason: {old_reason}")
            return True
        return False
