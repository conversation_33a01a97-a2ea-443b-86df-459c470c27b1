"""
Bollinger Bands mean-reversion trading strategy implementation.
Handles signal generation, entry/exit logic, and TP/SL calculation.
"""

import logging
from typing import Optional, Dict, Any, Tuple
from collections import deque
from enum import Enum
import pandas as pd
from alpaca.data.models import Bar
from config import TradingConfig
from utils import calculate_bollinger_bands, calculate_atr

logger = logging.getLogger(__name__)

class SignalType(Enum):
    """Trading signal types."""
    NO_SIGNAL = "no_signal"
    LONG_ENTRY = "long_entry"
    SHORT_ENTRY = "short_entry"
    LONG_EXIT = "long_exit"
    SHORT_EXIT = "short_exit"

class PositionSide(Enum):
    """Position side types."""
    LONG = "long"
    SHORT = "short"
    NONE = "none"

class TradingStrategy:
    """
    Bollinger Bands mean-reversion trading strategy.
    """
    
    def __init__(self):
        # Data storage for each symbol (symbol -> deque of bars)
        self.bar_data: Dict[str, deque] = {}
        
        # Current positions tracking (symbol -> position_side)
        self.positions: Dict[str, PositionSide] = {}
        
        # Strategy parameters
        self.bb_period = TradingConfig.BB_PERIOD
        self.bb_std_dev = TradingConfig.BB_STD_DEV
        self.atr_period = TradingConfig.ATR_PERIOD
        self.atr_stop_multiplier = TradingConfig.ATR_STOP_MULTIPLIER
        
        logger.info(f"Strategy initialized - BB Period: {self.bb_period}, BB StdDev: {self.bb_std_dev}, ATR Period: {self.atr_period}")
    
    def add_bar_data(self, symbol: str, bar: Bar) -> None:
        """
        Add new bar data for a symbol.
        
        Args:
            symbol: Stock symbol
            bar: New bar data
        """
        if symbol not in self.bar_data:
            self.bar_data[symbol] = deque(maxlen=TradingConfig.MAX_BARS_MEMORY)
        
        # Convert bar to dictionary for easier handling
        bar_dict = {
            'timestamp': bar.timestamp,
            'open': float(bar.open),
            'high': float(bar.high),
            'low': float(bar.low),
            'close': float(bar.close),
            'volume': int(bar.volume)
        }
        
        self.bar_data[symbol].append(bar_dict)
        
        logger.debug(f"Added bar data for {symbol}: Close=${bar.close:.2f}, Volume={bar.volume}")
    
    def _get_dataframe(self, symbol: str) -> Optional[pd.DataFrame]:
        """
        Convert deque of bar data to pandas DataFrame.
        
        Args:
            symbol: Stock symbol
            
        Returns:
            DataFrame with OHLCV data or None if insufficient data
        """
        if symbol not in self.bar_data or len(self.bar_data[symbol]) < max(self.bb_period, self.atr_period):
            return None
        
        try:
            df = pd.DataFrame(list(self.bar_data[symbol]))
            df.set_index('timestamp', inplace=True)
            return df
        except Exception as e:
            logger.error(f"Error creating DataFrame for {symbol}: {e}")
            return None
    
    def calculate_indicators(self, symbol: str) -> Optional[Dict[str, float]]:
        """
        Calculate technical indicators for a symbol.
        
        Args:
            symbol: Stock symbol
            
        Returns:
            Dictionary with indicator values or None if insufficient data
        """
        df = self._get_dataframe(symbol)
        if df is None:
            return None
        
        try:
            # Calculate Bollinger Bands
            upper_bb, middle_bb, lower_bb = calculate_bollinger_bands(
                df['close'], self.bb_period, self.bb_std_dev
            )
            
            # Calculate ATR
            atr = calculate_atr(df['high'], df['low'], df['close'], self.atr_period)
            
            # Get latest values
            latest_indicators = {
                'close': df['close'].iloc[-1],
                'upper_bb': upper_bb.iloc[-1] if not upper_bb.empty else None,
                'middle_bb': middle_bb.iloc[-1] if not middle_bb.empty else None,
                'lower_bb': lower_bb.iloc[-1] if not lower_bb.empty else None,
                'atr': atr.iloc[-1] if not atr.empty else None,
                'volume': df['volume'].iloc[-1]
            }
            
            # Check for NaN values
            for key, value in latest_indicators.items():
                if pd.isna(value):
                    logger.warning(f"NaN value for {key} in {symbol}")
                    return None
            
            return latest_indicators
            
        except Exception as e:
            logger.error(f"Error calculating indicators for {symbol}: {e}")
            return None
    
    def generate_signal(self, symbol: str) -> Tuple[SignalType, Dict[str, Any]]:
        """
        Generate trading signal for a symbol.
        
        Args:
            symbol: Stock symbol
            
        Returns:
            Tuple of (signal_type, signal_data)
        """
        indicators = self.calculate_indicators(symbol)
        if indicators is None:
            return SignalType.NO_SIGNAL, {}
        
        current_position = self.positions.get(symbol, PositionSide.NONE)
        close_price = indicators['close']
        upper_bb = indicators['upper_bb']
        middle_bb = indicators['middle_bb']
        lower_bb = indicators['lower_bb']
        atr = indicators['atr']
        
        signal_data = {
            'symbol': symbol,
            'close_price': close_price,
            'upper_bb': upper_bb,
            'middle_bb': middle_bb,
            'lower_bb': lower_bb,
            'atr': atr,
            'current_position': current_position.value
        }
        
        # Entry signals (only if no current position)
        if current_position == PositionSide.NONE:
            # Long entry: price crosses below lower Bollinger Band
            if close_price < lower_bb:
                signal_data.update({
                    'entry_price': close_price,
                    'take_profit': middle_bb,
                    'stop_loss': close_price - (atr * self.atr_stop_multiplier),
                    'side': 'buy'
                })
                logger.info(f"LONG ENTRY signal for {symbol} at ${close_price:.2f} (Lower BB: ${lower_bb:.2f})")
                return SignalType.LONG_ENTRY, signal_data
            
            # Short entry: price crosses above upper Bollinger Band
            elif close_price > upper_bb:
                signal_data.update({
                    'entry_price': close_price,
                    'take_profit': middle_bb,
                    'stop_loss': close_price + (atr * self.atr_stop_multiplier),
                    'side': 'sell'
                })
                logger.info(f"SHORT ENTRY signal for {symbol} at ${close_price:.2f} (Upper BB: ${upper_bb:.2f})")
                return SignalType.SHORT_ENTRY, signal_data
        
        # Exit signals (only if we have a position)
        elif current_position == PositionSide.LONG:
            # Long exit: price reaches middle band (take profit target)
            if close_price >= middle_bb:
                signal_data['exit_price'] = close_price
                logger.info(f"LONG EXIT signal for {symbol} at ${close_price:.2f} (Middle BB: ${middle_bb:.2f})")
                return SignalType.LONG_EXIT, signal_data
        
        elif current_position == PositionSide.SHORT:
            # Short exit: price reaches middle band (take profit target)
            if close_price <= middle_bb:
                signal_data['exit_price'] = close_price
                logger.info(f"SHORT EXIT signal for {symbol} at ${close_price:.2f} (Middle BB: ${middle_bb:.2f})")
                return SignalType.SHORT_EXIT, signal_data
        
        return SignalType.NO_SIGNAL, signal_data
    
    def update_position(self, symbol: str, position_side: PositionSide) -> None:
        """
        Update position tracking for a symbol.
        
        Args:
            symbol: Stock symbol
            position_side: New position side
        """
        old_position = self.positions.get(symbol, PositionSide.NONE)
        self.positions[symbol] = position_side
        
        if old_position != position_side:
            logger.info(f"Position updated for {symbol}: {old_position.value} -> {position_side.value}")
    
    def get_position(self, symbol: str) -> PositionSide:
        """
        Get current position for a symbol.
        
        Args:
            symbol: Stock symbol
            
        Returns:
            Current position side
        """
        return self.positions.get(symbol, PositionSide.NONE)
    
    def calculate_trade_levels(self, symbol: str, entry_price: float, side: str) -> Dict[str, float]:
        """
        Calculate take profit and stop loss levels for a trade.
        
        Args:
            symbol: Stock symbol
            entry_price: Entry price for the trade
            side: Trade side ('buy' or 'sell')
            
        Returns:
            Dictionary with take_profit and stop_loss prices
        """
        indicators = self.calculate_indicators(symbol)
        if indicators is None:
            logger.error(f"Cannot calculate trade levels for {symbol} - no indicators")
            return {}
        
        middle_bb = indicators['middle_bb']
        atr = indicators['atr']
        
        if side.lower() == 'buy':
            # Long position
            take_profit = middle_bb
            stop_loss = entry_price - (atr * self.atr_stop_multiplier)
        else:
            # Short position
            take_profit = middle_bb
            stop_loss = entry_price + (atr * self.atr_stop_multiplier)
        
        return {
            'take_profit': take_profit,
            'stop_loss': stop_loss,
            'atr': atr,
            'middle_bb': middle_bb
        }
    
    def get_strategy_stats(self) -> Dict[str, Any]:
        """
        Get current strategy statistics.
        
        Returns:
            Dictionary with strategy statistics
        """
        total_symbols = len(self.bar_data)
        symbols_with_positions = len([pos for pos in self.positions.values() if pos != PositionSide.NONE])
        long_positions = len([pos for pos in self.positions.values() if pos == PositionSide.LONG])
        short_positions = len([pos for pos in self.positions.values() if pos == PositionSide.SHORT])
        
        return {
            'total_symbols_tracked': total_symbols,
            'symbols_with_positions': symbols_with_positions,
            'long_positions': long_positions,
            'short_positions': short_positions,
            'bb_period': self.bb_period,
            'bb_std_dev': self.bb_std_dev,
            'atr_period': self.atr_period,
            'atr_stop_multiplier': self.atr_stop_multiplier
        }
    
    def clear_symbol_data(self, symbol: str) -> None:
        """
        Clear all data for a symbol (useful when removing from universe).
        
        Args:
            symbol: Stock symbol to clear
        """
        if symbol in self.bar_data:
            del self.bar_data[symbol]
        if symbol in self.positions:
            del self.positions[symbol]
        logger.info(f"Cleared all data for {symbol}")
    
    def has_sufficient_data(self, symbol: str) -> bool:
        """
        Check if we have sufficient data to generate signals for a symbol.
        
        Args:
            symbol: Stock symbol
            
        Returns:
            True if sufficient data is available
        """
        if symbol not in self.bar_data:
            return False
        
        required_bars = max(self.bb_period, self.atr_period)
        return len(self.bar_data[symbol]) >= required_bars
