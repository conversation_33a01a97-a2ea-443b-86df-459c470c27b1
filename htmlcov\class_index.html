<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_dca529e9.css" type="text/css">
    <script src="coverage_html_cb_497bf287.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">39%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>n</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button" href="index.html">Files</a>
                <a class="button" href="function_index.html">Functions</a>
                <a class="button current">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.10.5">coverage.py v7.10.5</a>,
            created at 2025-08-23 16:20 +0100
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="region" class="name left" aria-sort="none" data-default-sort-order="ascending" data-shortcut="n">class<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="config_py.html#t13">config.py</a></td>
                <td class="name left"><a href="config_py.html#t13"><data value='TradingConfig'>TradingConfig</data></a></td>
                <td>9</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="7 9">78%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="config_py.html">config.py</a></td>
                <td class="name left"><a href="config_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>38</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="38 38">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="main_py.html#t59">main.py</a></td>
                <td class="name left"><a href="main_py.html#t59"><data value='TradingBot'>TradingBot</data></a></td>
                <td>155</td>
                <td>58</td>
                <td>0</td>
                <td class="right" data-ratio="97 155">63%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="main_py.html">main.py</a></td>
                <td class="name left"><a href="main_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>55</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="35 55">64%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="order_manager_py.html#t20">order_manager.py</a></td>
                <td class="name left"><a href="order_manager_py.html#t20"><data value='OrderManager'>OrderManager</data></a></td>
                <td>133</td>
                <td>106</td>
                <td>0</td>
                <td class="right" data-ratio="27 133">20%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="order_manager_py.html">order_manager.py</a></td>
                <td class="name left"><a href="order_manager_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>25</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="25 25">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="risk_manager_py.html#t15">risk_manager.py</a></td>
                <td class="name left"><a href="risk_manager_py.html#t15"><data value='RiskManager'>RiskManager</data></a></td>
                <td>96</td>
                <td>40</td>
                <td>0</td>
                <td class="right" data-ratio="56 96">58%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="risk_manager_py.html">risk_manager.py</a></td>
                <td class="name left"><a href="risk_manager_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>19</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="19 19">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="run_tests_py.html">run_tests.py</a></td>
                <td class="name left"><a href="run_tests_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>77</td>
                <td>77</td>
                <td>0</td>
                <td class="right" data-ratio="0 77">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="run_tests_simple_py.html">run_tests_simple.py</a></td>
                <td class="name left"><a href="run_tests_simple_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>59</td>
                <td>59</td>
                <td>0</td>
                <td class="right" data-ratio="0 59">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="strategy_py.html#t17">strategy.py</a></td>
                <td class="name left"><a href="strategy_py.html#t17"><data value='SignalType'>SignalType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="strategy_py.html#t25">strategy.py</a></td>
                <td class="name left"><a href="strategy_py.html#t25"><data value='PositionSide'>PositionSide</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="strategy_py.html#t31">strategy.py</a></td>
                <td class="name left"><a href="strategy_py.html#t31"><data value='TradingStrategy'>TradingStrategy</data></a></td>
                <td>97</td>
                <td>39</td>
                <td>0</td>
                <td class="right" data-ratio="58 97">60%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="strategy_py.html">strategy.py</a></td>
                <td class="name left"><a href="strategy_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>31</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="31 31">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="test_quick_py.html">test_quick.py</a></td>
                <td class="name left"><a href="test_quick_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>139</td>
                <td>139</td>
                <td>0</td>
                <td class="right" data-ratio="0 139">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531___init___py.html">tests\__init__.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_conftest_py.html">tests\conftest.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_conftest_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>15</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="15 15">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_fixtures_py.html#t13">tests\fixtures.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_fixtures_py.html#t13"><data value='MockBar'>MockBar</data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_fixtures_py.html#t26">tests\fixtures.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_fixtures_py.html#t26"><data value='MockPosition'>MockPosition</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_fixtures_py.html#t34">tests\fixtures.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_fixtures_py.html#t34"><data value='MockAccount'>MockAccount</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_fixtures_py.html#t42">tests\fixtures.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_fixtures_py.html#t42"><data value='MockOrder'>MockOrder</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_fixtures_py.html#t52">tests\fixtures.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_fixtures_py.html#t52"><data value='MockAsset'>MockAsset</data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_fixtures_py.html#t240">tests\fixtures.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_fixtures_py.html#t240"><data value='MockRateLimiter'>MockRateLimiter</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_fixtures_py.html">tests\fixtures.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_fixtures_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>110</td>
                <td>73</td>
                <td>0</td>
                <td class="right" data-ratio="37 110">34%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_integration_py.html#t24">tests\test_integration.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_integration_py.html#t24"><data value='TestTradingWorkflow'>TestTradingWorkflow</data></a></td>
                <td>56</td>
                <td>56</td>
                <td>0</td>
                <td class="right" data-ratio="0 56">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_integration_py.html#t180">tests\test_integration.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_integration_py.html#t180"><data value='TestOrderManagerIntegration'>TestOrderManagerIntegration</data></a></td>
                <td>25</td>
                <td>25</td>
                <td>0</td>
                <td class="right" data-ratio="0 25">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_integration_py.html#t253">tests\test_integration.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_integration_py.html#t253"><data value='TestUniverseSelectionIntegration'>TestUniverseSelectionIntegration</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_integration_py.html#t303">tests\test_integration.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_integration_py.html#t303"><data value='TestEndToEndScenarios'>TestEndToEndScenarios</data></a></td>
                <td>36</td>
                <td>36</td>
                <td>0</td>
                <td class="right" data-ratio="0 36">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_integration_py.html">tests\test_integration.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_integration_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>35</td>
                <td>35</td>
                <td>0</td>
                <td class="right" data-ratio="0 35">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_risk_manager_py.html#t16">tests\test_risk_manager.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_risk_manager_py.html#t16"><data value='TestRiskManager'>TestRiskManager</data></a></td>
                <td>107</td>
                <td>107</td>
                <td>0</td>
                <td class="right" data-ratio="0 107">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_risk_manager_py.html">tests\test_risk_manager.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_risk_manager_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>32</td>
                <td>32</td>
                <td>0</td>
                <td class="right" data-ratio="0 32">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_strategy_py.html#t18">tests\test_strategy.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_strategy_py.html#t18"><data value='TestTradingStrategy'>TestTradingStrategy</data></a></td>
                <td>123</td>
                <td>123</td>
                <td>0</td>
                <td class="right" data-ratio="0 123">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_strategy_py.html">tests\test_strategy.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_strategy_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>29</td>
                <td>29</td>
                <td>0</td>
                <td class="right" data-ratio="0 29">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_system_py.html#t21">tests\test_system.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_system_py.html#t21"><data value='TestTradingBotSystem'>TestTradingBotSystem</data></a></td>
                <td>120</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="118 120">98%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_system_py.html#t301">tests\test_system.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_system_py.html#t301"><data value='TestSystemResilience'>TestSystemResilience</data></a></td>
                <td>32</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="30 32">94%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_system_py.html">tests\test_system.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_system_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>37</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="36 37">97%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_utils_py.html#t23">tests\test_utils.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_utils_py.html#t23"><data value='TestMarketHours'>TestMarketHours</data></a></td>
                <td>20</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="0 20">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_utils_py.html#t68">tests\test_utils.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_utils_py.html#t68"><data value='TestTechnicalIndicators'>TestTechnicalIndicators</data></a></td>
                <td>27</td>
                <td>27</td>
                <td>0</td>
                <td class="right" data-ratio="0 27">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_utils_py.html#t127">tests\test_utils.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_utils_py.html#t127"><data value='TestPositionSizing'>TestPositionSizing</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_utils_py.html#t156">tests\test_utils.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_utils_py.html#t156"><data value='TestUtilityFunctions'>TestUtilityFunctions</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_utils_py.html#t194">tests\test_utils.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_utils_py.html#t194"><data value='TestRateLimiter'>TestRateLimiter</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_utils_py.html">tests\test_utils.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_utils_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>39</td>
                <td>39</td>
                <td>0</td>
                <td class="right" data-ratio="0 39">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="universe_selector_py.html#t22">universe_selector.py</a></td>
                <td class="name left"><a href="universe_selector_py.html#t22"><data value='UniverseSelector'>UniverseSelector</data></a></td>
                <td>107</td>
                <td>25</td>
                <td>0</td>
                <td class="right" data-ratio="82 107">77%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="universe_selector_py.html">universe_selector.py</a></td>
                <td class="name left"><a href="universe_selector_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>22</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="22 22">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="utils_py.html#t197">utils.py</a></td>
                <td class="name left"><a href="utils_py.html#t197"><data value='RateLimiter'>RateLimiter</data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="utils_py.html">utils.py</a></td>
                <td class="name left"><a href="utils_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>79</td>
                <td>22</td>
                <td>0</td>
                <td class="right" data-ratio="57 79">72%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td class="name left">&nbsp;</td>
                <td>2071</td>
                <td>1258</td>
                <td>0</td>
                <td class="right" data-ratio="813 2071">39%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.10.5">coverage.py v7.10.5</a>,
            created at 2025-08-23 16:20 +0100
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href=""></a>
        <a id="nextFileLink" class="nav" href=""></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
