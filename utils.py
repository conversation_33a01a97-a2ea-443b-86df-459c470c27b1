"""
Utility functions for the trading bot including technical analysis,
market hours checking, and helper functions.
"""

import pandas as pd
import numpy as np
from datetime import datetime, time
import pytz
from typing import Optional, Tuple
import logging
from config import TradingConfig

logger = logging.getLogger(__name__)

def is_market_open() -> bool:
    """
    Check if the US stock market is currently open.
    
    Returns:
        bool: True if market is open, False otherwise
    """
    try:
        et_tz = pytz.timezone('US/Eastern')
        now_et = datetime.now(et_tz)
        
        # Check if it's a weekday (Monday=0, Sunday=6)
        if now_et.weekday() >= 5:  # Saturday or Sunday
            return False
        
        # Market hours: 9:30 AM - 4:00 PM ET
        market_open = time(TradingConfig.MARKET_OPEN_HOUR, TradingConfig.MARKET_OPEN_MINUTE)
        market_close = time(TradingConfig.MARKET_CLOSE_HOUR, TradingConfig.MARKET_CLOSE_MINUTE)
        
        current_time = now_et.time()
        
        return market_open <= current_time <= market_close
    
    except Exception as e:
        logger.error(f"Error checking market hours: {e}")
        return False

def calculate_bollinger_bands(prices: pd.Series, period: int = 20, std_dev: float = 2.0) -> Tuple[pd.Series, pd.Series, pd.Series]:
    """
    Calculate Bollinger Bands for a price series.
    
    Args:
        prices: Series of prices (typically close prices)
        period: Moving average period
        std_dev: Number of standard deviations for bands
    
    Returns:
        Tuple of (upper_band, middle_band, lower_band)
    """
    try:
        middle_band = prices.rolling(window=period).mean()
        std = prices.rolling(window=period).std()
        
        upper_band = middle_band + (std * std_dev)
        lower_band = middle_band - (std * std_dev)
        
        return upper_band, middle_band, lower_band
    
    except Exception as e:
        logger.error(f"Error calculating Bollinger Bands: {e}")
        return pd.Series(), pd.Series(), pd.Series()

def calculate_atr(high: pd.Series, low: pd.Series, close: pd.Series, period: int = 14) -> pd.Series:
    """
    Calculate Average True Range (ATR) for volatility measurement.
    
    Args:
        high: Series of high prices
        low: Series of low prices
        close: Series of close prices
        period: ATR period
    
    Returns:
        Series of ATR values
    """
    try:
        # Calculate True Range components
        tr1 = high - low
        tr2 = abs(high - close.shift(1))
        tr3 = abs(low - close.shift(1))
        
        # True Range is the maximum of the three
        true_range = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
        
        # ATR is the moving average of True Range
        atr = true_range.rolling(window=period).mean()
        
        return atr
    
    except Exception as e:
        logger.error(f"Error calculating ATR: {e}")
        return pd.Series()

def calculate_position_size(account_equity: float, entry_price: float, stop_loss_price: float, 
                          max_risk_pct: float = TradingConfig.MAX_POSITION_RISK) -> int:
    """
    Calculate position size based on risk management rules.
    
    Args:
        account_equity: Total account equity
        entry_price: Planned entry price
        stop_loss_price: Planned stop loss price
        max_risk_pct: Maximum risk percentage per trade
    
    Returns:
        Number of shares to trade
    """
    try:
        # Calculate risk per share
        risk_per_share = abs(entry_price - stop_loss_price)
        
        if risk_per_share <= 0:
            logger.warning("Invalid risk per share calculation")
            return 0
        
        # Calculate maximum dollar risk
        max_dollar_risk = account_equity * max_risk_pct
        
        # Calculate position size
        position_size = int(max_dollar_risk / risk_per_share)
        
        # Ensure minimum position size of 1 share
        return max(1, position_size)
    
    except Exception as e:
        logger.error(f"Error calculating position size: {e}")
        return 0

def format_currency(amount: float) -> str:
    """Format currency amount for display."""
    return f"${amount:,.2f}"

def format_percentage(value: float) -> str:
    """Format percentage for display."""
    return f"{value:.2%}"

def validate_symbol(symbol: str) -> bool:
    """
    Validate if a symbol is acceptable for trading.
    
    Args:
        symbol: Stock symbol to validate
    
    Returns:
        bool: True if symbol is valid for trading
    """
    if not symbol or len(symbol) > 5:
        return False
    
    # Check against excluded symbols
    if symbol in TradingConfig.get_excluded_symbols():
        return False
    
    return True

def calculate_drawdown(current_equity: float, peak_equity: float) -> float:
    """
    Calculate drawdown percentage.
    
    Args:
        current_equity: Current account equity
        peak_equity: Peak (high water mark) equity
    
    Returns:
        Drawdown as a decimal (e.g., 0.05 for 5%)
    """
    if peak_equity <= 0:
        return 0.0
    
    return max(0.0, (peak_equity - current_equity) / peak_equity)

def is_new_trading_day(last_check_time: Optional[datetime] = None) -> bool:
    """
    Check if we've entered a new trading day.
    
    Args:
        last_check_time: Last time this check was performed
    
    Returns:
        bool: True if it's a new trading day
    """
    if last_check_time is None:
        return True
    
    et_tz = pytz.timezone('US/Eastern')
    now_et = datetime.now(et_tz)
    last_et = last_check_time.astimezone(et_tz)
    
    # Check if the date has changed
    return now_et.date() != last_et.date()

class RateLimiter:
    """Simple rate limiter for API calls."""
    
    def __init__(self, max_calls: int, time_window: int):
        self.max_calls = max_calls
        self.time_window = time_window
        self.calls = []
    
    def can_make_call(self) -> bool:
        """Check if we can make an API call without exceeding rate limits."""
        now = datetime.now()
        
        # Remove calls outside the time window
        self.calls = [call_time for call_time in self.calls 
                     if (now - call_time).total_seconds() < self.time_window]
        
        return len(self.calls) < self.max_calls
    
    def record_call(self):
        """Record that an API call was made."""
        self.calls.append(datetime.now())
