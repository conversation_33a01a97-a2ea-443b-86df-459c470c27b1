"""
Main application loop for the autonomous trading bot.
Event-driven architecture with WebSocket data streaming and component orchestration.
"""

import asyncio
import logging
import signal
import sys
from datetime import datetime
from typing import Set, Optional
import structlog

# Alpaca imports
from alpaca.trading.client import TradingClient
from alpaca.data.historical import StockHistoricalDataClient
from alpaca.data.live import StockDataStream
from alpaca.data.models import Bar

# Local imports
from config import TradingConfig
from utils import is_market_open, format_currency, format_percentage
from universe_selector import UniverseSelector
from strategy import TradingStrategy, SignalType, PositionSide
from risk_manager import RiskManager
from order_manager import OrderManager

# Configure structured logging
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
        structlog.processors.JSONRenderer()
    ],
    context_class=dict,
    logger_factory=structlog.stdlib.LoggerFactory(),
    wrapper_class=structlog.stdlib.BoundLogger,
    cache_logger_on_first_use=True,
)

# Set up logging
logging.basicConfig(
    level=getattr(logging, TradingConfig.LOG_LEVEL),
    format=TradingConfig.LOG_FORMAT,
    handlers=[
        logging.FileHandler(TradingConfig.LOG_FILE),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

class TradingBot:
    """
    Main trading bot orchestrator with event-driven architecture.
    """
    
    def __init__(self):
        # Initialize clients
        self.trading_client = TradingClient(
            TradingConfig.ALPACA_API_KEY,
            TradingConfig.ALPACA_SECRET_KEY,
            paper=True  # Always use paper trading for safety
        )
        
        self.data_client = StockHistoricalDataClient(
            TradingConfig.ALPACA_API_KEY,
            TradingConfig.ALPACA_SECRET_KEY
        )
        
        # Initialize components
        self.universe_selector = UniverseSelector(self.trading_client, self.data_client)
        self.strategy = TradingStrategy()
        self.risk_manager = RiskManager()
        self.order_manager = OrderManager(self.trading_client)
        
        # WebSocket stream
        self.data_stream: Optional[StockDataStream] = None
        self.current_universe: Set[str] = set()
        
        # Control flags
        self.running = False
        self.shutdown_requested = False
        
        logger.info("Trading Bot initialized")
    
    async def initialize(self) -> bool:
        """
        Initialize the trading bot and all components.
        
        Returns:
            bool: True if initialization successful
        """
        try:
            logger.info("Initializing trading bot...")
            
            # Get account information
            account = self.trading_client.get_account()
            account_equity = float(account.equity)
            
            logger.info(f"Account Status: {account.status}")
            logger.info(f"Account Equity: {format_currency(account_equity)}")
            logger.info(f"Buying Power: {format_currency(float(account.buying_power))}")
            
            # Initialize risk manager with current equity
            self.risk_manager.initialize_equity(account_equity)
            
            # Initialize universe
            await self.universe_selector.update_universe()
            self.current_universe = self.universe_selector.get_current_universe()
            
            if not self.current_universe:
                logger.error("No trading universe selected, cannot continue")
                return False
            
            logger.info(f"Initial trading universe: {sorted(self.current_universe)}")
            
            # Initialize WebSocket stream
            self.data_stream = StockDataStream(
                TradingConfig.ALPACA_API_KEY,
                TradingConfig.ALPACA_SECRET_KEY
            )
            
            # Subscribe to bar data for all universe symbols
            self.data_stream.subscribe_bars(self.on_bar, *self.current_universe)
            
            logger.info("Trading bot initialization complete")
            return True
            
        except Exception as e:
            logger.error(f"Error during initialization: {e}")
            return False
    
    async def on_bar(self, bar: Bar) -> None:
        """
        Event handler for incoming bar data (core trading logic).
        
        Args:
            bar: New bar data from WebSocket
        """
        try:
            symbol = bar.symbol
            
            # Skip if not in current universe
            if symbol not in self.current_universe:
                return
            
            # Add bar data to strategy
            self.strategy.add_bar_data(symbol, bar)
            
            # Skip if insufficient data for analysis
            if not self.strategy.has_sufficient_data(symbol):
                return
            
            # Generate trading signal
            signal_type, signal_data = self.strategy.generate_signal(symbol)
            
            if signal_type == SignalType.NO_SIGNAL:
                return
            
            # Process the signal
            await self.process_signal(signal_type, signal_data)
            
        except Exception as e:
            logger.error(f"Error processing bar data for {bar.symbol}: {e}")
    
    async def process_signal(self, signal_type: SignalType, signal_data: dict) -> None:
        """
        Process a trading signal and execute trades if appropriate.
        
        Args:
            signal_type: Type of trading signal
            signal_data: Signal data dictionary
        """
        try:
            symbol = signal_data['symbol']
            
            # Handle entry signals
            if signal_type in [SignalType.LONG_ENTRY, SignalType.SHORT_ENTRY]:
                await self.handle_entry_signal(signal_type, signal_data)
            
            # Handle exit signals
            elif signal_type in [SignalType.LONG_EXIT, SignalType.SHORT_EXIT]:
                await self.handle_exit_signal(signal_type, signal_data)
                
        except Exception as e:
            logger.error(f"Error processing signal: {e}")
    
    async def handle_entry_signal(self, signal_type: SignalType, signal_data: dict) -> None:
        """Handle entry signals and place bracket orders."""
        try:
            symbol = signal_data['symbol']
            
            # Check if we already have a position
            if self.order_manager.has_position(symbol):
                logger.debug(f"Already have position in {symbol}, skipping entry signal")
                return
            
            # Validate trade with risk manager
            trade_validation = self.risk_manager.validate_trade(
                symbol,
                signal_data['entry_price'],
                signal_data['stop_loss'],
                signal_data['take_profit']
            )
            
            if not trade_validation['approved']:
                logger.info(f"Trade rejected for {symbol}: {trade_validation['reason']}")
                return
            
            # Submit bracket order
            order_id = await self.order_manager.submit_bracket_order(
                symbol=symbol,
                side=signal_data['side'],
                quantity=trade_validation['position_size'],
                take_profit_price=signal_data['take_profit'],
                stop_loss_price=signal_data['stop_loss']
            )
            
            if order_id:
                # Update strategy position tracking
                position_side = PositionSide.LONG if signal_data['side'] == 'buy' else PositionSide.SHORT
                self.strategy.update_position(symbol, position_side)
                
                logger.info(f"Entry order placed for {symbol}: {signal_data['side']} "
                           f"{trade_validation['position_size']} shares, "
                           f"Risk: {format_currency(trade_validation['risk_amount'])}")
            
        except Exception as e:
            logger.error(f"Error handling entry signal for {signal_data.get('symbol', 'unknown')}: {e}")
    
    async def handle_exit_signal(self, signal_type: SignalType, signal_data: dict) -> None:
        """Handle exit signals and close positions."""
        try:
            symbol = signal_data['symbol']
            
            # Check if we actually have a position
            if not self.order_manager.has_position(symbol):
                logger.debug(f"No position in {symbol}, skipping exit signal")
                return
            
            # Get position info to determine exit side and quantity
            positions = self.trading_client.get_all_positions()
            position = next((p for p in positions if p.symbol == symbol), None)
            
            if not position or float(position.qty) == 0:
                logger.warning(f"Position not found or zero quantity for {symbol}")
                return
            
            # Determine exit order details
            qty = abs(float(position.qty))
            side = 'sell' if float(position.qty) > 0 else 'buy'
            
            # Submit market order to exit
            order_id = await self.order_manager.submit_market_order(symbol, side, int(qty))
            
            if order_id:
                # Update strategy position tracking
                self.strategy.update_position(symbol, PositionSide.NONE)
                logger.info(f"Exit order placed for {symbol}: {side} {int(qty)} shares")
            
        except Exception as e:
            logger.error(f"Error handling exit signal for {signal_data.get('symbol', 'unknown')}: {e}")
    
    async def periodic_tasks(self) -> None:
        """Run periodic maintenance tasks."""
        try:
            # Update position cache
            await self.order_manager.update_position_cache()
            
            # Update risk manager with current equity
            account = self.trading_client.get_account()
            self.risk_manager.update_equity(float(account.equity))
            self.risk_manager.update_position_count(self.order_manager.get_position_count())
            
            # Update universe if needed
            universe_updated = await self.universe_selector.update_universe()
            if universe_updated:
                new_universe = self.universe_selector.get_current_universe()
                await self.update_subscriptions(new_universe)
            
            # Log status
            await self.log_status()
            
        except Exception as e:
            logger.error(f"Error in periodic tasks: {e}")
    
    async def update_subscriptions(self, new_universe: Set[str]) -> None:
        """Update WebSocket subscriptions when universe changes."""
        try:
            if self.data_stream and new_universe != self.current_universe:
                # Unsubscribe from old symbols
                old_symbols = self.current_universe - new_universe
                if old_symbols:
                    self.data_stream.unsubscribe_bars(*old_symbols)
                    for symbol in old_symbols:
                        self.strategy.clear_symbol_data(symbol)
                
                # Subscribe to new symbols
                new_symbols = new_universe - self.current_universe
                if new_symbols:
                    self.data_stream.subscribe_bars(self.on_bar, *new_symbols)
                
                self.current_universe = new_universe
                logger.info(f"Updated subscriptions: +{len(new_symbols)} -{len(old_symbols)}")
                
        except Exception as e:
            logger.error(f"Error updating subscriptions: {e}")
    
    async def log_status(self) -> None:
        """Log current bot status."""
        try:
            risk_metrics = self.risk_manager.get_risk_metrics()
            order_stats = self.order_manager.get_order_stats()
            strategy_stats = self.strategy.get_strategy_stats()
            
            logger.info("=== BOT STATUS ===")
            logger.info(f"Market Open: {is_market_open()}")
            logger.info(f"Current Equity: {format_currency(risk_metrics.get('current_equity', 0))}")
            logger.info(f"Daily Drawdown: {risk_metrics.get('daily_drawdown_pct', 'N/A')}")
            logger.info(f"Total Drawdown: {risk_metrics.get('total_drawdown_pct', 'N/A')}")
            logger.info(f"Open Positions: {order_stats['open_positions']}")
            logger.info(f"Trading Halted: {risk_metrics.get('trading_halted', False)}")
            logger.info(f"Universe Size: {len(self.current_universe)}")
            logger.info("==================")
            
        except Exception as e:
            logger.error(f"Error logging status: {e}")
    
    async def run(self) -> None:
        """Main event loop."""
        try:
            self.running = True
            logger.info("Starting trading bot main loop")
            
            # Start WebSocket stream
            if self.data_stream:
                stream_task = asyncio.create_task(self.data_stream._run_forever())
                await asyncio.sleep(2)  # Give stream time to connect
            
            # Main loop
            while self.running and not self.shutdown_requested:
                # Check if market is open
                if not is_market_open():
                    logger.info("Market is closed, waiting...")
                    await asyncio.sleep(300)  # Wait 5 minutes
                    continue
                
                # Run periodic tasks
                await self.periodic_tasks()
                
                # Wait before next iteration
                await asyncio.sleep(30)  # 30 second main loop
                
        except Exception as e:
            logger.error(f"Error in main loop: {e}")
        finally:
            await self.shutdown()
    
    async def shutdown(self) -> None:
        """Graceful shutdown."""
        try:
            logger.info("Shutting down trading bot...")
            self.running = False
            
            # Cancel all pending orders
            await self.order_manager.cancel_all_orders()
            
            # Close WebSocket connection
            if self.data_stream:
                await self.data_stream.close()
            
            logger.info("Trading bot shutdown complete")
            
        except Exception as e:
            logger.error(f"Error during shutdown: {e}")
    
    def signal_handler(self, signum, frame):
        """Handle shutdown signals."""
        logger.info(f"Received signal {signum}, initiating shutdown...")
        self.shutdown_requested = True

async def main():
    """Main entry point."""
    try:
        # Create and initialize bot
        bot = TradingBot()
        
        # Set up signal handlers
        signal.signal(signal.SIGINT, bot.signal_handler)
        signal.signal(signal.SIGTERM, bot.signal_handler)
        
        # Initialize bot
        if not await bot.initialize():
            logger.error("Bot initialization failed")
            return 1
        
        # Run bot
        await bot.run()
        return 0
        
    except Exception as e:
        logger.error(f"Fatal error: {e}")
        return 1

if __name__ == "__main__":
    try:
        # Use uvloop on Unix systems for better performance
        if sys.platform != "win32":
            import uvloop
            uvloop.install()
    except ImportError:
        pass
    
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
