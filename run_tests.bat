@echo off
REM Run tests for the trading bot within the virtual environment
REM This script provides options for different test suites

echo ========================================
echo TRADING BOT - TEST RUNNER
echo ========================================
echo.

REM Check if virtual environment exists
if not exist "venv" (
    echo ERROR: Virtual environment not found!
    echo Please run 'setup_venv.bat' first to create the environment.
    echo.
    pause
    exit /b 1
)

REM Activate virtual environment
echo Activating virtual environment...
call venv\Scripts\activate.bat
if errorlevel 1 (
    echo ERROR: Failed to activate virtual environment
    echo Try running 'setup_venv.bat' to recreate the environment.
    pause
    exit /b 1
)

echo.
echo Python environment:
python --version
echo.

REM Show test options
echo Available test options:
echo.
echo 1. Quick Validation Tests (recommended first)
echo 2. Unit Tests Only
echo 3. Integration Tests Only
echo 4. System Tests Only
echo 5. All Tests (comprehensive)
echo 6. All Tests with Coverage Report
echo 7. Simple Test Runner (no coverage dependencies)
echo 8. Custom pytest command
echo.

set /p choice="Select test option (1-8): "

if "%choice%"=="1" goto :quick_tests
if "%choice%"=="2" goto :unit_tests
if "%choice%"=="3" goto :integration_tests
if "%choice%"=="4" goto :system_tests
if "%choice%"=="5" goto :all_tests
if "%choice%"=="6" goto :coverage_tests
if "%choice%"=="7" goto :simple_tests
if "%choice%"=="8" goto :custom_tests

echo Invalid choice. Running quick tests by default...
goto :quick_tests

:quick_tests
echo.
echo ========================================
echo RUNNING QUICK VALIDATION TESTS
echo ========================================
echo.
echo These tests validate core functionality without external dependencies.
echo Perfect for rapid development validation.
echo.
python test_quick.py
goto :end

:unit_tests
echo.
echo ========================================
echo RUNNING UNIT TESTS
echo ========================================
echo.
python run_tests.py --suite unit --verbose
goto :end

:integration_tests
echo.
echo ========================================
echo RUNNING INTEGRATION TESTS
echo ========================================
echo.
python run_tests.py --suite integration --verbose
goto :end

:system_tests
echo.
echo ========================================
echo RUNNING SYSTEM TESTS
echo ========================================
echo.
python run_tests.py --suite system --verbose
goto :end

:all_tests
echo.
echo ========================================
echo RUNNING ALL TESTS
echo ========================================
echo.
echo This will run the complete test suite (may take several minutes)
echo.
python run_tests.py --suite all --verbose
goto :end

:coverage_tests
echo.
echo ========================================
echo RUNNING ALL TESTS WITH COVERAGE
echo ========================================
echo.
echo This will run all tests and generate a coverage report.
echo.
python run_tests.py --suite all --verbose --coverage
if not errorlevel 1 (
    echo.
    echo Coverage report generated!
    echo Opening coverage report in browser...
    start htmlcov\index.html
)
goto :end

:simple_tests
echo.
echo ========================================
echo RUNNING SIMPLE TEST RUNNER
echo ========================================
echo.
echo Using simple test runner without coverage dependencies.
echo This is useful if you're having issues with pytest-cov.
echo.
python run_tests_simple.py --suite all --verbose
goto :end

:custom_tests
echo.
echo Enter custom pytest command (without 'pytest'):
echo Example: tests/test_utils.py -v -k "test_bollinger"
echo.
set /p custom_cmd="pytest "
if not "%custom_cmd%"=="" (
    echo.
    echo Running: pytest %custom_cmd%
    echo.
    pytest %custom_cmd%
)
goto :end

:end
echo.
echo ========================================
echo TEST EXECUTION COMPLETE
echo ========================================
echo.

REM Check if there were any test failures
if errorlevel 1 (
    echo Some tests failed. Please review the output above.
    echo.
    echo Common test failure causes:
    echo - Missing dependencies (run setup_venv.bat)
    echo - Configuration issues
    echo - Code changes that broke existing functionality
    echo.
    echo Recommendations:
    echo 1. Fix any failing tests before deploying
    echo 2. Run quick tests during development
    echo 3. Run full test suite before market deployment
    echo.
) else (
    echo All tests passed successfully! ✅
    echo.
    echo Your trading bot is ready for deployment.
    echo.
    echo Next steps:
    echo 1. Verify your .env file has correct API keys
    echo 2. Start with paper trading: run_bot.bat
    echo 3. Monitor performance and logs carefully
    echo.
)

echo.
echo Additional test commands:
echo - Quick tests: python test_quick.py
echo - Specific test file: pytest tests/test_utils.py -v
echo - Test with pattern: pytest -k "test_bollinger" -v
echo - Debug mode: pytest --pdb tests/test_utils.py
echo.

echo Press any key to exit...
pause >nul
