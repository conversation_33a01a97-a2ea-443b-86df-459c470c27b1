#!/usr/bin/env python3
"""
Test runner script for the trading bot.
Provides easy way to run different test suites.
"""

import sys
import subprocess
import argparse
from pathlib import Path

def run_command(cmd, description):
    """Run a command and handle the result."""
    print(f"\n{'='*60}")
    print(f"Running: {description}")
    print(f"Command: {' '.join(cmd)}")
    print(f"{'='*60}")
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, check=False)
        
        if result.stdout:
            print("STDOUT:")
            print(result.stdout)
        
        if result.stderr:
            print("STDERR:")
            print(result.stderr)
        
        if result.returncode == 0:
            print(f"✅ {description} - PASSED")
            return True
        else:
            print(f"❌ {description} - FAILED (exit code: {result.returncode})")
            return False
            
    except Exception as e:
        print(f"❌ {description} - ERROR: {e}")
        return False

def main():
    parser = argparse.ArgumentParser(description="Run trading bot tests")
    parser.add_argument("--suite", choices=["unit", "integration", "system", "all"], 
                       default="all", help="Test suite to run")
    parser.add_argument("--verbose", "-v", action="store_true", 
                       help="Verbose output")
    parser.add_argument("--coverage", action="store_true",
                       help="Run with coverage reporting")
    parser.add_argument("--install-deps", action="store_true",
                       help="Install test dependencies first")
    
    args = parser.parse_args()
    
    # Check if we're in the right directory
    if not Path("main.py").exists():
        print("❌ Error: Please run this script from the project root directory")
        sys.exit(1)
    
    # Install dependencies if requested
    if args.install_deps:
        print("Installing test dependencies...")
        install_cmd = [sys.executable, "-m", "pip", "install", "-r", "requirements.txt"]
        if not run_command(install_cmd, "Installing dependencies"):
            print("❌ Failed to install dependencies")
            sys.exit(1)
    
    # Base pytest command
    base_cmd = [sys.executable, "-m", "pytest"]
    
    if args.verbose:
        base_cmd.append("-v")
    
    if args.coverage:
        base_cmd.extend(["--cov=.", "--cov-report=html", "--cov-report=term"])
    
    # Add asyncio mode
    base_cmd.append("--asyncio-mode=auto")
    
    results = []
    
    if args.suite == "unit" or args.suite == "all":
        cmd = base_cmd + ["tests/test_utils.py", "tests/test_strategy.py", "tests/test_risk_manager.py"]
        results.append(run_command(cmd, "Unit Tests"))
    
    if args.suite == "integration" or args.suite == "all":
        cmd = base_cmd + ["tests/test_integration.py"]
        results.append(run_command(cmd, "Integration Tests"))
    
    if args.suite == "system" or args.suite == "all":
        cmd = base_cmd + ["tests/test_system.py"]
        results.append(run_command(cmd, "System Tests"))
    
    # Summary
    print(f"\n{'='*60}")
    print("TEST SUMMARY")
    print(f"{'='*60}")
    
    passed = sum(results)
    total = len(results)
    
    if passed == total:
        print(f"✅ All tests passed! ({passed}/{total})")
        if args.coverage:
            print("\n📊 Coverage report generated in htmlcov/index.html")
        sys.exit(0)
    else:
        print(f"❌ Some tests failed ({passed}/{total} passed)")
        sys.exit(1)

if __name__ == "__main__":
    main()
