"""
Unit tests for strategy.py - Trading strategy and signal generation.
"""

import pytest
import pandas as pd
from datetime import datetime
from unittest.mock import MagicMock, patch
from collections import deque

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from strategy import TradingStrategy, SignalType, PositionSide
from alpaca.data.models import Bar

class TestTradingStrategy:
    """Test trading strategy functionality."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.strategy = TradingStrategy()
        self.test_symbol = "AAPL"
    
    def create_mock_bar(self, open_price, high, low, close, volume=1000, timestamp=None):
        """Create a mock bar for testing."""
        if timestamp is None:
            timestamp = datetime.now()
        
        bar = MagicMock(spec=Bar)
        bar.symbol = self.test_symbol
        bar.timestamp = timestamp
        bar.open = open_price
        bar.high = high
        bar.low = low
        bar.close = close
        bar.volume = volume
        return bar
    
    def add_test_bars(self, prices, volumes=None):
        """Add a series of test bars to the strategy."""
        if volumes is None:
            volumes = [1000] * len(prices)
        
        for i, price in enumerate(prices):
            bar = self.create_mock_bar(
                open_price=price,
                high=price + 0.5,
                low=price - 0.5,
                close=price,
                volume=volumes[i]
            )
            self.strategy.add_bar_data(self.test_symbol, bar)
    
    def test_add_bar_data(self):
        """Test adding bar data to strategy."""
        bar = self.create_mock_bar(100.0, 101.0, 99.0, 100.5)
        
        self.strategy.add_bar_data(self.test_symbol, bar)
        
        assert self.test_symbol in self.strategy.bar_data
        assert len(self.strategy.bar_data[self.test_symbol]) == 1
        
        bar_dict = self.strategy.bar_data[self.test_symbol][0]
        assert bar_dict['close'] == 100.5
        assert bar_dict['volume'] == 1000
    
    def test_insufficient_data_for_indicators(self):
        """Test behavior with insufficient data for indicators."""
        # Add only a few bars (less than required for BB/ATR)
        self.add_test_bars([100, 101, 102])
        
        indicators = self.strategy.calculate_indicators(self.test_symbol)
        assert indicators is None
        
        assert not self.strategy.has_sufficient_data(self.test_symbol)
    
    def test_sufficient_data_for_indicators(self):
        """Test indicator calculation with sufficient data."""
        # Add enough bars for BB (20) and ATR (14) calculation
        prices = list(range(100, 125))  # 25 prices
        self.add_test_bars(prices)
        
        assert self.strategy.has_sufficient_data(self.test_symbol)
        
        indicators = self.strategy.calculate_indicators(self.test_symbol)
        assert indicators is not None
        assert 'close' in indicators
        assert 'upper_bb' in indicators
        assert 'middle_bb' in indicators
        assert 'lower_bb' in indicators
        assert 'atr' in indicators
    
    def test_long_entry_signal_generation(self):
        """Test long entry signal when price crosses below lower BB."""
        # Create data that will generate a long signal
        # Start with higher prices, then drop below lower BB
        prices = [100] * 20 + [95, 94, 93]  # Price drops significantly
        self.add_test_bars(prices)
        
        signal_type, signal_data = self.strategy.generate_signal(self.test_symbol)
        
        # Should generate long entry signal
        if signal_type == SignalType.LONG_ENTRY:
            assert signal_data['side'] == 'buy'
            assert 'entry_price' in signal_data
            assert 'take_profit' in signal_data
            assert 'stop_loss' in signal_data
            assert signal_data['stop_loss'] < signal_data['entry_price']
    
    def test_short_entry_signal_generation(self):
        """Test short entry signal when price crosses above upper BB."""
        # Create data that will generate a short signal
        # Start with lower prices, then spike above upper BB
        prices = [100] * 20 + [105, 106, 107]  # Price spikes significantly
        self.add_test_bars(prices)
        
        signal_type, signal_data = self.strategy.generate_signal(self.test_symbol)
        
        # Should generate short entry signal
        if signal_type == SignalType.SHORT_ENTRY:
            assert signal_data['side'] == 'sell'
            assert 'entry_price' in signal_data
            assert 'take_profit' in signal_data
            assert 'stop_loss' in signal_data
            assert signal_data['stop_loss'] > signal_data['entry_price']
    
    def test_no_signal_with_existing_position(self):
        """Test that no entry signal is generated when position exists."""
        # Set up data for potential signal
        prices = [100] * 20 + [95, 94, 93]
        self.add_test_bars(prices)
        
        # Set existing long position
        self.strategy.update_position(self.test_symbol, PositionSide.LONG)
        
        signal_type, signal_data = self.strategy.generate_signal(self.test_symbol)
        
        # Should not generate entry signal due to existing position
        assert signal_type not in [SignalType.LONG_ENTRY, SignalType.SHORT_ENTRY]
    
    def test_long_exit_signal_generation(self):
        """Test long exit signal when price reaches middle BB."""
        # Set up long position
        self.strategy.update_position(self.test_symbol, PositionSide.LONG)
        
        # Create data where price returns to middle BB
        prices = [95] * 15 + [100] * 10  # Price recovers to middle
        self.add_test_bars(prices)
        
        signal_type, signal_data = self.strategy.generate_signal(self.test_symbol)
        
        # Should generate long exit signal when price reaches middle BB
        if signal_type == SignalType.LONG_EXIT:
            assert 'exit_price' in signal_data
    
    def test_short_exit_signal_generation(self):
        """Test short exit signal when price reaches middle BB."""
        # Set up short position
        self.strategy.update_position(self.test_symbol, PositionSide.SHORT)
        
        # Create data where price returns to middle BB
        prices = [105] * 15 + [100] * 10  # Price drops back to middle
        self.add_test_bars(prices)
        
        signal_type, signal_data = self.strategy.generate_signal(self.test_symbol)
        
        # Should generate short exit signal when price reaches middle BB
        if signal_type == SignalType.SHORT_EXIT:
            assert 'exit_price' in signal_data
    
    def test_position_tracking(self):
        """Test position tracking functionality."""
        # Initially no position
        assert self.strategy.get_position(self.test_symbol) == PositionSide.NONE
        
        # Update to long position
        self.strategy.update_position(self.test_symbol, PositionSide.LONG)
        assert self.strategy.get_position(self.test_symbol) == PositionSide.LONG
        
        # Update to short position
        self.strategy.update_position(self.test_symbol, PositionSide.SHORT)
        assert self.strategy.get_position(self.test_symbol) == PositionSide.SHORT
        
        # Clear position
        self.strategy.update_position(self.test_symbol, PositionSide.NONE)
        assert self.strategy.get_position(self.test_symbol) == PositionSide.NONE
    
    def test_calculate_trade_levels(self):
        """Test trade level calculation."""
        # Add sufficient data
        prices = list(range(95, 120))  # 25 prices
        self.add_test_bars(prices)
        
        # Calculate trade levels for long position
        levels = self.strategy.calculate_trade_levels(self.test_symbol, 100.0, 'buy')
        
        assert 'take_profit' in levels
        assert 'stop_loss' in levels
        assert 'atr' in levels
        assert 'middle_bb' in levels
        
        # For long position, stop loss should be below entry
        assert levels['stop_loss'] < 100.0
        
        # Calculate trade levels for short position
        levels = self.strategy.calculate_trade_levels(self.test_symbol, 100.0, 'sell')
        
        # For short position, stop loss should be above entry
        assert levels['stop_loss'] > 100.0
    
    def test_strategy_stats(self):
        """Test strategy statistics."""
        # Add data for multiple symbols
        symbols = ["AAPL", "MSFT", "GOOGL"]
        for symbol in symbols:
            bar = self.create_mock_bar(100.0, 101.0, 99.0, 100.5)
            bar.symbol = symbol
            self.strategy.add_bar_data(symbol, bar)
        
        # Set some positions
        self.strategy.update_position("AAPL", PositionSide.LONG)
        self.strategy.update_position("MSFT", PositionSide.SHORT)
        
        stats = self.strategy.get_strategy_stats()
        
        assert stats['total_symbols_tracked'] == 3
        assert stats['symbols_with_positions'] == 2
        assert stats['long_positions'] == 1
        assert stats['short_positions'] == 1
        assert stats['bb_period'] == 20
        assert stats['atr_period'] == 14
    
    def test_clear_symbol_data(self):
        """Test clearing symbol data."""
        # Add data and position
        self.add_test_bars([100, 101, 102])
        self.strategy.update_position(self.test_symbol, PositionSide.LONG)
        
        # Verify data exists
        assert self.test_symbol in self.strategy.bar_data
        assert self.test_symbol in self.strategy.positions
        
        # Clear data
        self.strategy.clear_symbol_data(self.test_symbol)
        
        # Verify data is cleared
        assert self.test_symbol not in self.strategy.bar_data
        assert self.test_symbol not in self.strategy.positions
    
    def test_dataframe_conversion(self):
        """Test conversion of deque data to DataFrame."""
        # Add test data
        prices = [100, 101, 102, 103, 104]
        self.add_test_bars(prices)
        
        # Get DataFrame
        df = self.strategy._get_dataframe(self.test_symbol)
        
        assert df is not None
        assert len(df) == 5
        assert 'open' in df.columns
        assert 'high' in df.columns
        assert 'low' in df.columns
        assert 'close' in df.columns
        assert 'volume' in df.columns
        
        # Verify data integrity
        assert df['close'].iloc[-1] == 104.0

if __name__ == "__main__":
    pytest.main([__file__])
