"""
Order management system with bracket order handling,
position cache management, and API rate limiting.
"""

import asyncio
import logging
from typing import Dict, Set, Optional, Any, List
from datetime import datetime, timedelta
from alpaca.trading.client import TradingClient
from alpaca.trading.requests import MarketOrderRequest, OrderRequest
from alpaca.trading.enums import OrderSide, TimeInForce, OrderType
from alpaca.common.exceptions import APIError
from config import TradingConfig
from utils import RateLimiter, format_currency
from strategy import PositionSide

logger = logging.getLogger(__name__)

class OrderManager:
    """
    Manages order submission, position tracking, and API rate limiting.
    """
    
    def __init__(self, trading_client: TradingClient):
        self.trading_client = trading_client
        
        # Position cache (symbols with open positions)
        self.position_cache: Set[str] = set()
        self.last_position_update: datetime = datetime.min
        
        # Rate limiting
        self.rate_limiter = RateLimiter(
            TradingConfig.API_RATE_LIMIT,
            TradingConfig.API_RATE_WINDOW
        )
        
        # Order tracking
        self.pending_orders: Dict[str, Dict[str, Any]] = {}  # symbol -> order_info
        self.failed_orders: List[Dict[str, Any]] = []
        
        logger.info("Order Manager initialized")
    
    async def update_position_cache(self) -> bool:
        """
        Update the position cache with current open positions.
        
        Returns:
            bool: True if update was successful
        """
        try:
            now = datetime.now()
            time_since_update = (now - self.last_position_update).total_seconds()
            
            if time_since_update < TradingConfig.POSITION_CACHE_REFRESH_INTERVAL:
                return True
            
            if not self.rate_limiter.can_make_call():
                logger.warning("Rate limit reached, skipping position cache update")
                return False
            
            # Get all open positions
            positions = self.trading_client.get_all_positions()
            self.rate_limiter.record_call()
            
            # Update cache
            new_cache = set()
            for position in positions:
                if float(position.qty) != 0:  # Only include non-zero positions
                    new_cache.add(position.symbol)
            
            # Log changes
            added_positions = new_cache - self.position_cache
            removed_positions = self.position_cache - new_cache
            
            if added_positions:
                logger.info(f"New positions detected: {sorted(added_positions)}")
            if removed_positions:
                logger.info(f"Positions closed: {sorted(removed_positions)}")
            
            self.position_cache = new_cache
            self.last_position_update = now
            
            logger.debug(f"Position cache updated: {len(self.position_cache)} open positions")
            return True
            
        except Exception as e:
            logger.error(f"Error updating position cache: {e}")
            return False
    
    def has_position(self, symbol: str) -> bool:
        """
        Check if we have an open position in a symbol.
        
        Args:
            symbol: Stock symbol
            
        Returns:
            bool: True if position exists
        """
        return symbol in self.position_cache
    
    def get_position_count(self) -> int:
        """Get the current number of open positions."""
        return len(self.position_cache)
    
    async def submit_bracket_order(self, symbol: str, side: str, quantity: int, 
                                 take_profit_price: float, stop_loss_price: float) -> Optional[str]:
        """
        Submit a bracket order (market entry with TP and SL).
        
        Args:
            symbol: Stock symbol
            side: Order side ('buy' or 'sell')
            quantity: Number of shares
            take_profit_price: Take profit price
            stop_loss_price: Stop loss price
            
        Returns:
            Order ID if successful, None otherwise
        """
        try:
            if not self.rate_limiter.can_make_call():
                logger.warning(f"Rate limit reached, cannot submit order for {symbol}")
                return None
            
            # Determine order side
            order_side = OrderSide.BUY if side.lower() == 'buy' else OrderSide.SELL
            
            # Create bracket order request
            # Note: Alpaca's bracket orders are created using the main order with take_profit and stop_loss parameters
            order_data = MarketOrderRequest(
                symbol=symbol,
                qty=quantity,
                side=order_side,
                time_in_force=TimeInForce.DAY,
                take_profit={'limit_price': take_profit_price},
                stop_loss={'stop_price': stop_loss_price}
            )
            
            # Submit the order
            order = self.trading_client.submit_order(order_data)
            self.rate_limiter.record_call()
            
            # Track the order
            order_info = {
                'order_id': order.id,
                'symbol': symbol,
                'side': side,
                'quantity': quantity,
                'take_profit_price': take_profit_price,
                'stop_loss_price': stop_loss_price,
                'submitted_at': datetime.now(),
                'status': 'submitted'
            }
            
            self.pending_orders[symbol] = order_info
            
            logger.info(f"Bracket order submitted for {symbol}: {side} {quantity} shares, "
                       f"TP: ${take_profit_price:.2f}, SL: ${stop_loss_price:.2f}")
            
            return order.id
            
        except APIError as e:
            logger.error(f"API error submitting bracket order for {symbol}: {e}")
            self._record_failed_order(symbol, side, quantity, str(e))
            return None
        except Exception as e:
            logger.error(f"Unexpected error submitting bracket order for {symbol}: {e}")
            self._record_failed_order(symbol, side, quantity, str(e))
            return None
    
    async def submit_market_order(self, symbol: str, side: str, quantity: int) -> Optional[str]:
        """
        Submit a simple market order (for exits).
        
        Args:
            symbol: Stock symbol
            side: Order side ('buy' or 'sell')
            quantity: Number of shares
            
        Returns:
            Order ID if successful, None otherwise
        """
        try:
            if not self.rate_limiter.can_make_call():
                logger.warning(f"Rate limit reached, cannot submit market order for {symbol}")
                return None
            
            order_side = OrderSide.BUY if side.lower() == 'buy' else OrderSide.SELL
            
            order_data = MarketOrderRequest(
                symbol=symbol,
                qty=quantity,
                side=order_side,
                time_in_force=TimeInForce.DAY
            )
            
            order = self.trading_client.submit_order(order_data)
            self.rate_limiter.record_call()
            
            logger.info(f"Market order submitted for {symbol}: {side} {quantity} shares")
            return order.id
            
        except APIError as e:
            logger.error(f"API error submitting market order for {symbol}: {e}")
            return None
        except Exception as e:
            logger.error(f"Unexpected error submitting market order for {symbol}: {e}")
            return None
    
    def _record_failed_order(self, symbol: str, side: str, quantity: int, error: str) -> None:
        """Record a failed order for analysis."""
        failed_order = {
            'symbol': symbol,
            'side': side,
            'quantity': quantity,
            'error': error,
            'failed_at': datetime.now()
        }
        
        self.failed_orders.append(failed_order)
        
        # Keep only recent failed orders (last 100)
        if len(self.failed_orders) > 100:
            self.failed_orders = self.failed_orders[-100:]
    
    async def cancel_all_orders(self, symbol: Optional[str] = None) -> bool:
        """
        Cancel all open orders, optionally for a specific symbol.
        
        Args:
            symbol: Optional symbol to cancel orders for (None = all orders)
            
        Returns:
            bool: True if successful
        """
        try:
            if not self.rate_limiter.can_make_call():
                logger.warning("Rate limit reached, cannot cancel orders")
                return False
            
            if symbol:
                # Cancel orders for specific symbol
                orders = self.trading_client.get_orders(symbols=[symbol])
            else:
                # Cancel all orders
                orders = self.trading_client.get_orders()
            
            self.rate_limiter.record_call()
            
            cancelled_count = 0
            for order in orders:
                if order.status in ['new', 'partially_filled', 'pending_new']:
                    try:
                        if self.rate_limiter.can_make_call():
                            self.trading_client.cancel_order_by_id(order.id)
                            self.rate_limiter.record_call()
                            cancelled_count += 1
                        else:
                            break
                    except Exception as e:
                        logger.error(f"Error cancelling order {order.id}: {e}")
            
            if cancelled_count > 0:
                logger.info(f"Cancelled {cancelled_count} orders" + (f" for {symbol}" if symbol else ""))
            
            return True
            
        except Exception as e:
            logger.error(f"Error cancelling orders: {e}")
            return False
    
    async def liquidate_all_positions(self) -> bool:
        """
        Liquidate all open positions (emergency function).
        
        Returns:
            bool: True if liquidation was initiated
        """
        try:
            logger.critical("LIQUIDATING ALL POSITIONS")
            
            if not self.rate_limiter.can_make_call():
                logger.error("Rate limit reached, cannot liquidate positions")
                return False
            
            # Get all positions
            positions = self.trading_client.get_all_positions()
            self.rate_limiter.record_call()
            
            liquidated_count = 0
            for position in positions:
                qty = float(position.qty)
                if qty == 0:
                    continue
                
                try:
                    # Determine side for liquidation
                    side = 'sell' if qty > 0 else 'buy'
                    quantity = abs(int(qty))
                    
                    order_id = await self.submit_market_order(position.symbol, side, quantity)
                    if order_id:
                        liquidated_count += 1
                        logger.info(f"Liquidated position: {position.symbol} {side} {quantity}")
                    
                    # Small delay between orders
                    await asyncio.sleep(0.1)
                    
                except Exception as e:
                    logger.error(f"Error liquidating position {position.symbol}: {e}")
            
            logger.critical(f"Liquidation initiated for {liquidated_count} positions")
            return liquidated_count > 0
            
        except Exception as e:
            logger.error(f"Error in liquidation process: {e}")
            return False
    
    def get_order_stats(self) -> Dict[str, Any]:
        """
        Get order management statistics.
        
        Returns:
            Dictionary with order statistics
        """
        recent_failures = [
            order for order in self.failed_orders
            if (datetime.now() - order['failed_at']).total_seconds() < 3600  # Last hour
        ]
        
        return {
            'open_positions': len(self.position_cache),
            'position_symbols': sorted(list(self.position_cache)),
            'pending_orders': len(self.pending_orders),
            'total_failed_orders': len(self.failed_orders),
            'recent_failed_orders': len(recent_failures),
            'last_position_update': self.last_position_update.isoformat() if self.last_position_update != datetime.min else None,
            'rate_limiter_calls': len(self.rate_limiter.calls)
        }
    
    def clear_pending_order(self, symbol: str) -> None:
        """Clear pending order tracking for a symbol."""
        if symbol in self.pending_orders:
            del self.pending_orders[symbol]
    
    def get_pending_order(self, symbol: str) -> Optional[Dict[str, Any]]:
        """Get pending order info for a symbol."""
        return self.pending_orders.get(symbol)
